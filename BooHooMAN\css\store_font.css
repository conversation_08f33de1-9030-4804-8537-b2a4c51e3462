/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
}
[dir] body {
  margin: 0;
}
main {
  display: block;
}
h1 {
  font-size: 2em;
}
[dir] h1 {
  margin: 0.67em 0;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
pre {
  font-family: monospace, monospace;
  font-size: 1em;
}
[dir] a {
  background-color: transparent;
}
abbr[title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
[dir] abbr[title] {
  border-bottom: none;
}
b,
strong {
  font-weight: bolder;
}
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
[dir] img {
  border-style: none;
}
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
}
[dir] button,
[dir] input,
[dir] optgroup,
[dir] select,
[dir] textarea {
  margin: 0;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
[dir] [type="button"]::-moz-focus-inner,
[dir] [type="reset"]::-moz-focus-inner,
[dir] [type="submit"]::-moz-focus-inner,
[dir] button::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring,
button:-moz-focusring {
  outline: 1px dotted ButtonText;
}
[dir] fieldset {
  padding: 0.35em 0.75em 0.625em;
}
legend {
  box-sizing: border-box;
  color: inherit;
  display: table;
  max-width: 100%;
  white-space: normal;
}
[dir] legend {
  padding: 0;
}
progress {
  vertical-align: baseline;
}
textarea {
  overflow: auto;
}
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
}
[dir] [type="checkbox"],
[dir] [type="radio"] {
  padding: 0;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
details {
  display: block;
}
summary {
  display: list-item;
}
[hidden],
template {
  display: none;
}
* {
  box-sizing: border-box;
}
html {
  width: 100%;
  height: 100%;
}
body {
  font-family: inherit;
  color: #1a1a1a;
  font-size: 14px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  text-rendering: optimizeLegibility;
  min-height: 100vh;
}
[dir] h1,
[dir] h2,
[dir] h3,
[dir] h4,
[dir] h5,
[dir] h6 {
  margin: 0;
}
[dir] p {
  margin-top: 0;
}
[dir] form {
  margin-bottom: 0;
}
[dir] label {
  cursor: pointer;
}
input,
textarea {
  outline: none;
}
input[type="email"],
input[type="number"],
input[type="password"],
input[type="search"],
input[type="tel"],
input[type="text"],
input[type="url"],
textarea {
  height: 50px;
  width: 100%;
  transition: all 0.25s;
  caret-color: var(--primary-color);
}
[dir] input[type="email"],
[dir] input[type="number"],
[dir] input[type="password"],
[dir] input[type="search"],
[dir] input[type="tel"],
[dir] input[type="text"],
[dir] input[type="url"],
[dir] textarea {
  border-radius: 3px;
  border: 1px solid #e5e5e5;
  padding: 10px 15px;
  background-color: transparent;
}
[dir] input[type="email"]:focus,
[dir] input[type="number"]:focus,
[dir] input[type="password"]:focus,
[dir] input[type="search"]:focus,
[dir] input[type="tel"]:focus,
[dir] input[type="text"]:focus,
[dir] input[type="url"]:focus,
[dir] textarea:focus {
  border: 2px solid var(--primary-color);
  box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.05);
}
input[type="email"]:focus::-moz-placeholder,
input[type="number"]:focus::-moz-placeholder,
input[type="password"]:focus::-moz-placeholder,
input[type="search"]:focus::-moz-placeholder,
input[type="tel"]:focus::-moz-placeholder,
input[type="text"]:focus::-moz-placeholder,
input[type="url"]:focus::-moz-placeholder,
textarea:focus::-moz-placeholder {
  opacity: 0;
}
input[type="email"]:focus:-ms-input-placeholder,
input[type="number"]:focus:-ms-input-placeholder,
input[type="password"]:focus:-ms-input-placeholder,
input[type="search"]:focus:-ms-input-placeholder,
input[type="tel"]:focus:-ms-input-placeholder,
input[type="text"]:focus:-ms-input-placeholder,
input[type="url"]:focus:-ms-input-placeholder,
textarea:focus:-ms-input-placeholder {
  opacity: 0;
}
input[type="email"]:focus::placeholder,
input[type="number"]:focus::placeholder,
input[type="password"]:focus::placeholder,
input[type="search"]:focus::placeholder,
input[type="tel"]:focus::placeholder,
input[type="text"]:focus::placeholder,
input[type="url"]:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0;
}
input[type="email"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="text"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
textarea::-moz-placeholder {
  color: hsla(0, 0%, 46.7%, 0.6);
  -moz-transition: opacity 0.25s;
  transition: opacity 0.25s;
}
input[type="email"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="text"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: hsla(0, 0%, 46.7%, 0.6);
  -ms-transition: opacity 0.25s;
  transition: opacity 0.25s;
}
input[type="email"]::placeholder,
input[type="number"]::placeholder,
input[type="password"]::placeholder,
input[type="search"]::placeholder,
input[type="tel"]::placeholder,
input[type="text"]::placeholder,
input[type="url"]::placeholder,
textarea::placeholder {
  color: hsla(0, 0%, 46.7%, 0.6);
  transition: opacity 0.25s;
}
[dir] input[type="email"]:disabled,
[dir] input[type="number"]:disabled,
[dir] input[type="password"]:disabled,
[dir] input[type="search"]:disabled,
[dir] input[type="tel"]:disabled,
[dir] input[type="text"]:disabled,
[dir] input[type="url"]:disabled,
[dir] textarea:disabled {
  background-color: #f9f9f9;
  cursor: not-allowed;
}
input[type="number"] {
  -moz-appearance: textfield;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
}
[dir] input[type="number"]::-webkit-inner-spin-button,
[dir] input[type="number"]::-webkit-outer-spin-button {
  margin: 0;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}
select {
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
[dir] select {
  border-radius: 3px;
  cursor: pointer;
  border: 0;
  padding: 0 15px;
}
select option {
  color: #1a1a1a;
}
textarea {
  min-height: 130px;
  height: auto;
  resize: none;
}
button,
input[type="submit"] {
  outline: none;
  transition: all 0.25s;
}
[dir] button,
[dir] input[type="submit"] {
  /* background-color: transparent; */
  border: 0;
  padding: 0;
  cursor: pointer;
}
[dir] button:disabled,
[dir] input[type="submit"]:disabled {
  cursor: not-allowed;
}
::-moz-placeholder {
  opacity: 1;
}
[dir] ol,
[dir] ul {
  margin-bottom: 0;
}
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  vertical-align: middle;
  font-weight: 400;
}
[dir="ltr"] td,
[dir="ltr"] th {
  text-align: left;
}
[dir="rtl"] td,
[dir="rtl"] th {
  text-align: right;
}
a,
a:active,
a:hover {
  color: var(--primary-color);
  text-decoration: none;
  outline: none;
}
img {
  display: block;
  max-width: 100%;
  height: auto;
}

/*!
 *  Font Awesome 4.7.0 by @davegandy - http://fontawesome.io - @fontawesome
 *  License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
 */
@font-face {
  font-family: FontAwesome;
  src: url(/store-front/fonts/fontawesome/fontawesome-webfont.eot?v=4.7.0);
  src: url(/store-front/fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.7.0)
      format("embedded-opentype"),
    url(/store-front/fonts/fontawesome/fontawesome-webfont.woff2?v=4.7.0)
      format("woff2"),
    url(/store-front/fonts/fontawesome/fontawesome-webfont.woff?v=4.7.0)
      format("woff"),
    url(/store-front/fonts/fontawesome/fontawesome-webfont.ttf?v=4.7.0)
      format("truetype"),
    url(/store-front/fonts/fontawesome/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular)
      format("svg");
  font-weight: 400;
  font-style: normal;
}
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[dir="ltr"] .fa-spin {
  -webkit-animation: fa-spin-ltr 2s linear infinite;
  animation: fa-spin-ltr 2s linear infinite;
}
[dir="rtl"] .fa-spin {
  -webkit-animation: fa-spin-rtl 2s linear infinite;
  animation: fa-spin-rtl 2s linear infinite;
}
@-webkit-keyframes fa-spin-ltr {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
@-webkit-keyframes fa-spin-rtl {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-359deg);
  }
}
@keyframes fa-spin-ltr {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
@keyframes fa-spin-rtl {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-359deg);
  }
}
.fa-glass:before {
  content: "\F000";
}
.fa-music:before {
  content: "\F001";
}
.fa-search:before {
  content: "\F002";
}
.fa-envelope-o:before {
  content: "\F003";
}
.fa-heart:before {
  content: "\F004";
}
.fa-star:before {
  content: "\F005";
}
.fa-star-o:before {
  content: "\F006";
}
.fa-user:before {
  content: "\F007";
}
.fa-film:before {
  content: "\F008";
}
.fa-th-large:before {
  content: "\F009";
}
.fa-th:before {
  content: "\F00A";
}
.fa-th-list:before {
  content: "\F00B";
}
.fa-check:before {
  content: "\F00C";
}
.fa-close:before,
.fa-remove:before,
.fa-times:before {
  content: "\F00D";
}
.fa-search-plus:before {
  content: "\F00E";
}
.fa-search-minus:before {
  content: "\F010";
}
.fa-power-off:before {
  content: "\F011";
}
.fa-signal:before {
  content: "\F012";
}
.fa-cog:before,
.fa-gear:before {
  content: "\F013";
}
.fa-trash-o:before {
  content: "\F014";
}
.fa-home:before {
  content: "\F015";
}
.fa-file-o:before {
  content: "\F016";
}
.fa-clock-o:before {
  content: "\F017";
}
.fa-road:before {
  content: "\F018";
}
.fa-download:before {
  content: "\F019";
}
.fa-arrow-circle-o-down:before {
  content: "\F01A";
}
.fa-arrow-circle-o-up:before {
  content: "\F01B";
}
.fa-inbox:before {
  content: "\F01C";
}
.fa-play-circle-o:before {
  content: "\F01D";
}
.fa-repeat:before,
.fa-rotate-right:before {
  content: "\F01E";
}
.fa-refresh:before {
  content: "\F021";
}
.fa-list-alt:before {
  content: "\F022";
}
.fa-lock:before {
  content: "\F023";
}
.fa-flag:before {
  content: "\F024";
}
.fa-headphones:before {
  content: "\F025";
}
.fa-volume-off:before {
  content: "\F026";
}
.fa-volume-down:before {
  content: "\F027";
}
.fa-volume-up:before {
  content: "\F028";
}
.fa-qrcode:before {
  content: "\F029";
}
.fa-barcode:before {
  content: "\F02A";
}
.fa-tag:before {
  content: "\F02B";
}
.fa-tags:before {
  content: "\F02C";
}
.fa-book:before {
  content: "\F02D";
}
.fa-bookmark:before {
  content: "\F02E";
}
.fa-print:before {
  content: "\F02F";
}
.fa-camera:before {
  content: "\F030";
}
.fa-font:before {
  content: "\F031";
}
.fa-bold:before {
  content: "\F032";
}
.fa-italic:before {
  content: "\F033";
}
.fa-text-height:before {
  content: "\F034";
}
.fa-text-width:before {
  content: "\F035";
}
.fa-align-left:before {
  content: "\F036";
}
.fa-align-center:before {
  content: "\F037";
}
.fa-align-right:before {
  content: "\F038";
}
.fa-align-justify:before {
  content: "\F039";
}
.fa-list:before {
  content: "\F03A";
}
.fa-dedent:before,
.fa-outdent:before {
  content: "\F03B";
}
.fa-indent:before {
  content: "\F03C";
}
.fa-video-camera:before {
  content: "\F03D";
}
.fa-image:before,
.fa-photo:before,
.fa-picture-o:before {
  content: "\F03E";
}
.fa-pencil:before {
  content: "\F040";
}
.fa-map-marker:before {
  content: "\F041";
}
.fa-adjust:before {
  content: "\F042";
}
.fa-tint:before {
  content: "\F043";
}
.fa-edit:before,
.fa-pencil-square-o:before {
  content: "\F044";
}
.fa-share-square-o:before {
  content: "\F045";
}
.fa-check-square-o:before {
  content: "\F046";
}
.fa-arrows:before {
  content: "\F047";
}
.fa-step-backward:before {
  content: "\F048";
}
.fa-fast-backward:before {
  content: "\F049";
}
.fa-backward:before {
  content: "\F04A";
}
.fa-play:before {
  content: "\F04B";
}
.fa-pause:before {
  content: "\F04C";
}
.fa-stop:before {
  content: "\F04D";
}
.fa-forward:before {
  content: "\F04E";
}
.fa-fast-forward:before {
  content: "\F050";
}
.fa-step-forward:before {
  content: "\F051";
}
.fa-eject:before {
  content: "\F052";
}
.fa-chevron-left:before {
  content: "\F053";
}
.fa-chevron-right:before {
  content: "\F054";
}
.fa-plus-circle:before {
  content: "\F055";
}
.fa-minus-circle:before {
  content: "\F056";
}
.fa-times-circle:before {
  content: "\F057";
}
.fa-check-circle:before {
  content: "\F058";
}
.fa-question-circle:before {
  content: "\F059";
}
.fa-info-circle:before {
  content: "\F05A";
}
.fa-crosshairs:before {
  content: "\F05B";
}
.fa-times-circle-o:before {
  content: "\F05C";
}
.fa-check-circle-o:before {
  content: "\F05D";
}
.fa-ban:before {
  content: "\F05E";
}
.fa-arrow-left:before {
  content: "\F060";
}
.fa-arrow-right:before {
  content: "\F061";
}
.fa-arrow-up:before {
  content: "\F062";
}
.fa-arrow-down:before {
  content: "\F063";
}
.fa-mail-forward:before,
.fa-share:before {
  content: "\F064";
}
.fa-expand:before {
  content: "\F065";
}
.fa-compress:before {
  content: "\F066";
}
.fa-plus:before {
  content: "\F067";
}
.fa-minus:before {
  content: "\F068";
}
.fa-asterisk:before {
  content: "\F069";
}
.fa-exclamation-circle:before {
  content: "\F06A";
}
.fa-gift:before {
  content: "\F06B";
}
.fa-leaf:before {
  content: "\F06C";
}
.fa-fire:before {
  content: "\F06D";
}
.fa-eye:before {
  content: "\F06E";
}
.fa-eye-slash:before {
  content: "\F070";
}
.fa-exclamation-triangle:before,
.fa-warning:before {
  content: "\F071";
}
.fa-plane:before {
  content: "\F072";
}
.fa-calendar:before {
  content: "\F073";
}
.fa-random:before {
  content: "\F074";
}
.fa-comment:before {
  content: "\F075";
}
.fa-magnet:before {
  content: "\F076";
}
.fa-chevron-up:before {
  content: "\F077";
}
.fa-chevron-down:before {
  content: "\F078";
}
.fa-retweet:before {
  content: "\F079";
}
.fa-shopping-cart:before {
  content: "\F07A";
}
.fa-folder:before {
  content: "\F07B";
}
.fa-folder-open:before {
  content: "\F07C";
}
.fa-arrows-v:before {
  content: "\F07D";
}
.fa-arrows-h:before {
  content: "\F07E";
}
.fa-bar-chart-o:before,
.fa-bar-chart:before {
  content: "\F080";
}
.fa-twitter-square:before {
  content: "\F081";
}
.fa-facebook-square:before {
  content: "\F082";
}
.fa-camera-retro:before {
  content: "\F083";
}
.fa-key:before {
  content: "\F084";
}
.fa-cogs:before,
.fa-gears:before {
  content: "\F085";
}
.fa-comments:before {
  content: "\F086";
}
.fa-thumbs-o-up:before {
  content: "\F087";
}
.fa-thumbs-o-down:before {
  content: "\F088";
}
.fa-star-half:before {
  content: "\F089";
}
.fa-heart-o:before {
  content: "\F08A";
}
.fa-sign-out:before {
  content: "\F08B";
}
.fa-linkedin-square:before {
  content: "\F08C";
}
.fa-thumb-tack:before {
  content: "\F08D";
}
.fa-external-link:before {
  content: "\F08E";
}
.fa-sign-in:before {
  content: "\F090";
}
.fa-trophy:before {
  content: "\F091";
}
.fa-github-square:before {
  content: "\F092";
}
.fa-upload:before {
  content: "\F093";
}
.fa-lemon-o:before {
  content: "\F094";
}
.fa-phone:before {
  content: "\F095";
}
.fa-square-o:before {
  content: "\F096";
}
.fa-bookmark-o:before {
  content: "\F097";
}
.fa-phone-square:before {
  content: "\F098";
}
.fa-twitter:before {
  content: "\F099";
}
.fa-facebook-f:before,
.fa-facebook:before {
  content: "\F09A";
}
.fa-github:before {
  content: "\F09B";
}
.fa-unlock:before {
  content: "\F09C";
}
.fa-credit-card:before {
  content: "\F09D";
}
.fa-feed:before,
.fa-rss:before {
  content: "\F09E";
}
.fa-hdd-o:before {
  content: "\F0A0";
}
.fa-bullhorn:before {
  content: "\F0A1";
}
.fa-bell:before {
  content: "\F0F3";
}
.fa-certificate:before {
  content: "\F0A3";
}
.fa-hand-o-right:before {
  content: "\F0A4";
}
.fa-hand-o-left:before {
  content: "\F0A5";
}
.fa-hand-o-up:before {
  content: "\F0A6";
}
.fa-hand-o-down:before {
  content: "\F0A7";
}
.fa-arrow-circle-left:before {
  content: "\F0A8";
}
.fa-arrow-circle-right:before {
  content: "\F0A9";
}
.fa-arrow-circle-up:before {
  content: "\F0AA";
}
.fa-arrow-circle-down:before {
  content: "\F0AB";
}
.fa-globe:before {
  content: "\F0AC";
}
.fa-wrench:before {
  content: "\F0AD";
}
.fa-tasks:before {
  content: "\F0AE";
}
.fa-filter:before {
  content: "\F0B0";
}
.fa-briefcase:before {
  content: "\F0B1";
}
.fa-arrows-alt:before {
  content: "\F0B2";
}
.fa-group:before,
.fa-users:before {
  content: "\F0C0";
}
.fa-chain:before,
.fa-link:before {
  content: "\F0C1";
}
.fa-cloud:before {
  content: "\F0C2";
}
.fa-flask:before {
  content: "\F0C3";
}
.fa-cut:before,
.fa-scissors:before {
  content: "\F0C4";
}
.fa-copy:before,
.fa-files-o:before {
  content: "\F0C5";
}
.fa-paperclip:before {
  content: "\F0C6";
}
.fa-floppy-o:before,
.fa-save:before {
  content: "\F0C7";
}
.fa-square:before {
  content: "\F0C8";
}
.fa-bars:before,
.fa-navicon:before,
.fa-reorder:before {
  content: "\F0C9";
}
.fa-list-ul:before {
  content: "\F0CA";
}
.fa-list-ol:before {
  content: "\F0CB";
}
.fa-strikethrough:before {
  content: "\F0CC";
}
.fa-underline:before {
  content: "\F0CD";
}
.fa-table:before {
  content: "\F0CE";
}
.fa-magic:before {
  content: "\F0D0";
}
.fa-truck:before {
  content: "\F0D1";
}
.fa-pinterest:before {
  content: "\F0D2";
}
.fa-pinterest-square:before {
  content: "\F0D3";
}
.fa-google-plus-square:before {
  content: "\F0D4";
}
.fa-google-plus:before {
  content: "\F0D5";
}
.fa-money:before {
  content: "\F0D6";
}
.fa-caret-down:before {
  content: "\F0D7";
}
.fa-caret-up:before {
  content: "\F0D8";
}
.fa-caret-left:before {
  content: "\F0D9";
}
.fa-caret-right:before {
  content: "\F0DA";
}
.fa-columns:before {
  content: "\F0DB";
}
.fa-sort:before,
.fa-unsorted:before {
  content: "\F0DC";
}
.fa-sort-desc:before,
.fa-sort-down:before {
  content: "\F0DD";
}
.fa-sort-asc:before,
.fa-sort-up:before {
  content: "\F0DE";
}
.fa-envelope:before {
  content: "\F0E0";
}
.fa-linkedin:before {
  content: "\F0E1";
}
.fa-rotate-left:before,
.fa-undo:before {
  content: "\F0E2";
}
.fa-gavel:before,
.fa-legal:before {
  content: "\F0E3";
}
.fa-dashboard:before,
.fa-tachometer:before {
  content: "\F0E4";
}
.fa-comment-o:before {
  content: "\F0E5";
}
.fa-comments-o:before {
  content: "\F0E6";
}
.fa-bolt:before,
.fa-flash:before {
  content: "\F0E7";
}
.fa-sitemap:before {
  content: "\F0E8";
}
.fa-umbrella:before {
  content: "\F0E9";
}
.fa-clipboard:before,
.fa-paste:before {
  content: "\F0EA";
}
.fa-lightbulb-o:before {
  content: "\F0EB";
}
.fa-exchange:before {
  content: "\F0EC";
}
.fa-cloud-download:before {
  content: "\F0ED";
}
.fa-cloud-upload:before {
  content: "\F0EE";
}
.fa-user-md:before {
  content: "\F0F0";
}
.fa-stethoscope:before {
  content: "\F0F1";
}
.fa-suitcase:before {
  content: "\F0F2";
}
.fa-bell-o:before {
  content: "\F0A2";
}
.fa-coffee:before {
  content: "\F0F4";
}
.fa-cutlery:before {
  content: "\F0F5";
}
.fa-file-text-o:before {
  content: "\F0F6";
}
.fa-building-o:before {
  content: "\F0F7";
}
.fa-hospital-o:before {
  content: "\F0F8";
}
.fa-ambulance:before {
  content: "\F0F9";
}
.fa-medkit:before {
  content: "\F0FA";
}
.fa-fighter-jet:before {
  content: "\F0FB";
}
.fa-beer:before {
  content: "\F0FC";
}
.fa-h-square:before {
  content: "\F0FD";
}
.fa-plus-square:before {
  content: "\F0FE";
}
.fa-angle-double-left:before {
  content: "\F100";
}
.fa-angle-double-right:before {
  content: "\F101";
}
.fa-angle-double-up:before {
  content: "\F102";
}
.fa-angle-double-down:before {
  content: "\F103";
}
.fa-angle-left:before {
  content: "\F104";
}
.fa-angle-right:before {
  content: "\F105";
}
.fa-angle-up:before {
  content: "\F106";
}
.fa-angle-down:before {
  content: "\F107";
}
.fa-desktop:before {
  content: "\F108";
}
.fa-laptop:before {
  content: "\F109";
}
.fa-tablet:before {
  content: "\F10A";
}
.fa-mobile-phone:before,
.fa-mobile:before {
  content: "\F10B";
}
.fa-circle-o:before {
  content: "\F10C";
}
.fa-quote-left:before {
  content: "\F10D";
}
.fa-quote-right:before {
  content: "\F10E";
}
.fa-spinner:before {
  content: "\F110";
}
.fa-circle:before {
  content: "\F111";
}
.fa-mail-reply:before,
.fa-reply:before {
  content: "\F112";
}
.fa-github-alt:before {
  content: "\F113";
}
.fa-folder-o:before {
  content: "\F114";
}
.fa-folder-open-o:before {
  content: "\F115";
}
.fa-smile-o:before {
  content: "\F118";
}
.fa-frown-o:before {
  content: "\F119";
}
.fa-meh-o:before {
  content: "\F11A";
}
.fa-gamepad:before {
  content: "\F11B";
}
.fa-keyboard-o:before {
  content: "\F11C";
}
.fa-flag-o:before {
  content: "\F11D";
}
.fa-flag-checkered:before {
  content: "\F11E";
}
.fa-terminal:before {
  content: "\F120";
}
.fa-code:before {
  content: "\F121";
}
.fa-mail-reply-all:before,
.fa-reply-all:before {
  content: "\F122";
}
.fa-star-half-empty:before,
.fa-star-half-full:before,
.fa-star-half-o:before {
  content: "\F123";
}
.fa-location-arrow:before {
  content: "\F124";
}
.fa-crop:before {
  content: "\F125";
}
.fa-code-fork:before {
  content: "\F126";
}
.fa-chain-broken:before,
.fa-unlink:before {
  content: "\F127";
}
.fa-question:before {
  content: "\F128";
}
.fa-info:before {
  content: "\F129";
}
.fa-exclamation:before {
  content: "\F12A";
}
.fa-superscript:before {
  content: "\F12B";
}
.fa-subscript:before {
  content: "\F12C";
}
.fa-eraser:before {
  content: "\F12D";
}
.fa-puzzle-piece:before {
  content: "\F12E";
}
.fa-microphone:before {
  content: "\F130";
}
.fa-microphone-slash:before {
  content: "\F131";
}
.fa-shield:before {
  content: "\F132";
}
.fa-calendar-o:before {
  content: "\F133";
}
.fa-fire-extinguisher:before {
  content: "\F134";
}
.fa-rocket:before {
  content: "\F135";
}
.fa-maxcdn:before {
  content: "\F136";
}
.fa-chevron-circle-left:before {
  content: "\F137";
}
.fa-chevron-circle-right:before {
  content: "\F138";
}
.fa-chevron-circle-up:before {
  content: "\F139";
}
.fa-chevron-circle-down:before {
  content: "\F13A";
}
.fa-html5:before {
  content: "\F13B";
}
.fa-css3:before {
  content: "\F13C";
}
.fa-anchor:before {
  content: "\F13D";
}
.fa-unlock-alt:before {
  content: "\F13E";
}
.fa-bullseye:before {
  content: "\F140";
}
.fa-ellipsis-h:before {
  content: "\F141";
}
.fa-ellipsis-v:before {
  content: "\F142";
}
.fa-rss-square:before {
  content: "\F143";
}
.fa-play-circle:before {
  content: "\F144";
}
.fa-ticket:before {
  content: "\F145";
}
.fa-minus-square:before {
  content: "\F146";
}
.fa-minus-square-o:before {
  content: "\F147";
}
.fa-level-up:before {
  content: "\F148";
}
.fa-level-down:before {
  content: "\F149";
}
.fa-check-square:before {
  content: "\F14A";
}
.fa-pencil-square:before {
  content: "\F14B";
}
.fa-external-link-square:before {
  content: "\F14C";
}
.fa-share-square:before {
  content: "\F14D";
}
.fa-compass:before {
  content: "\F14E";
}
.fa-caret-square-o-down:before,
.fa-toggle-down:before {
  content: "\F150";
}
.fa-caret-square-o-up:before,
.fa-toggle-up:before {
  content: "\F151";
}
.fa-caret-square-o-right:before,
.fa-toggle-right:before {
  content: "\F152";
}
.fa-eur:before,
.fa-euro:before {
  content: "\F153";
}
.fa-gbp:before {
  content: "\F154";
}
.fa-dollar:before,
.fa-usd:before {
  content: "\F155";
}
.fa-inr:before,
.fa-rupee:before {
  content: "\F156";
}
.fa-cny:before,
.fa-jpy:before,
.fa-rmb:before,
.fa-yen:before {
  content: "\F157";
}
.fa-rouble:before,
.fa-rub:before,
.fa-ruble:before {
  content: "\F158";
}
.fa-krw:before,
.fa-won:before {
  content: "\F159";
}
.fa-bitcoin:before,
.fa-btc:before {
  content: "\F15A";
}
.fa-file:before {
  content: "\F15B";
}
.fa-file-text:before {
  content: "\F15C";
}
.fa-sort-alpha-asc:before {
  content: "\F15D";
}
.fa-sort-alpha-desc:before {
  content: "\F15E";
}
.fa-sort-amount-asc:before {
  content: "\F160";
}
.fa-sort-amount-desc:before {
  content: "\F161";
}
.fa-sort-numeric-asc:before {
  content: "\F162";
}
.fa-sort-numeric-desc:before {
  content: "\F163";
}
.fa-thumbs-up:before {
  content: "\F164";
}
.fa-thumbs-down:before {
  content: "\F165";
}
.fa-youtube-square:before {
  content: "\F166";
}
.fa-youtube:before {
  content: "\F167";
}
.fa-xing:before {
  content: "\F168";
}
.fa-xing-square:before {
  content: "\F169";
}
.fa-youtube-play:before {
  content: "\F16A";
}
.fa-dropbox:before {
  content: "\F16B";
}
.fa-stack-overflow:before {
  content: "\F16C";
}
.fa-instagram:before {
  content: "\F16D";
}
.fa-flickr:before {
  content: "\F16E";
}
.fa-adn:before {
  content: "\F170";
}
.fa-bitbucket:before {
  content: "\F171";
}
.fa-bitbucket-square:before {
  content: "\F172";
}
.fa-tumblr:before {
  content: "\F173";
}
.fa-tumblr-square:before {
  content: "\F174";
}
.fa-long-arrow-down:before {
  content: "\F175";
}
.fa-long-arrow-up:before {
  content: "\F176";
}
.fa-long-arrow-left:before {
  content: "\F177";
}
.fa-long-arrow-right:before {
  content: "\F178";
}
.fa-apple:before {
  content: "\F179";
}
.fa-windows:before {
  content: "\F17A";
}
.fa-android:before {
  content: "\F17B";
}
.fa-linux:before {
  content: "\F17C";
}
.fa-dribbble:before {
  content: "\F17D";
}
.fa-skype:before {
  content: "\F17E";
}
.fa-foursquare:before {
  content: "\F180";
}
.fa-trello:before {
  content: "\F181";
}
.fa-female:before {
  content: "\F182";
}
.fa-male:before {
  content: "\F183";
}
.fa-gittip:before,
.fa-gratipay:before {
  content: "\F184";
}
.fa-sun-o:before {
  content: "\F185";
}
.fa-moon-o:before {
  content: "\F186";
}
.fa-archive:before {
  content: "\F187";
}
.fa-bug:before {
  content: "\F188";
}
.fa-vk:before {
  content: "\F189";
}
.fa-weibo:before {
  content: "\F18A";
}
.fa-renren:before {
  content: "\F18B";
}
.fa-pagelines:before {
  content: "\F18C";
}
.fa-stack-exchange:before {
  content: "\F18D";
}
.fa-arrow-circle-o-right:before {
  content: "\F18E";
}
.fa-arrow-circle-o-left:before {
  content: "\F190";
}
.fa-caret-square-o-left:before,
.fa-toggle-left:before {
  content: "\F191";
}
.fa-dot-circle-o:before {
  content: "\F192";
}
.fa-wheelchair:before {
  content: "\F193";
}
.fa-vimeo-square:before {
  content: "\F194";
}
.fa-try:before,
.fa-turkish-lira:before {
  content: "\F195";
}
.fa-plus-square-o:before {
  content: "\F196";
}
.fa-space-shuttle:before {
  content: "\F197";
}
.fa-slack:before {
  content: "\F198";
}
.fa-envelope-square:before {
  content: "\F199";
}
.fa-wordpress:before {
  content: "\F19A";
}
.fa-openid:before {
  content: "\F19B";
}
.fa-bank:before,
.fa-institution:before,
.fa-university:before {
  content: "\F19C";
}
.fa-graduation-cap:before,
.fa-mortar-board:before {
  content: "\F19D";
}
.fa-yahoo:before {
  content: "\F19E";
}
.fa-google:before {
  content: "\F1A0";
}
.fa-reddit:before {
  content: "\F1A1";
}
.fa-reddit-square:before {
  content: "\F1A2";
}
.fa-stumbleupon-circle:before {
  content: "\F1A3";
}
.fa-stumbleupon:before {
  content: "\F1A4";
}
.fa-delicious:before {
  content: "\F1A5";
}
.fa-digg:before {
  content: "\F1A6";
}
.fa-pied-piper-pp:before {
  content: "\F1A7";
}
.fa-pied-piper-alt:before {
  content: "\F1A8";
}
.fa-drupal:before {
  content: "\F1A9";
}
.fa-joomla:before {
  content: "\F1AA";
}
.fa-language:before {
  content: "\F1AB";
}
.fa-fax:before {
  content: "\F1AC";
}
.fa-building:before {
  content: "\F1AD";
}
.fa-child:before {
  content: "\F1AE";
}
.fa-paw:before {
  content: "\F1B0";
}
.fa-spoon:before {
  content: "\F1B1";
}
.fa-cube:before {
  content: "\F1B2";
}
.fa-cubes:before {
  content: "\F1B3";
}
.fa-behance:before {
  content: "\F1B4";
}
.fa-behance-square:before {
  content: "\F1B5";
}
.fa-steam:before {
  content: "\F1B6";
}
.fa-steam-square:before {
  content: "\F1B7";
}
.fa-recycle:before {
  content: "\F1B8";
}
.fa-automobile:before,
.fa-car:before {
  content: "\F1B9";
}
.fa-cab:before,
.fa-taxi:before {
  content: "\F1BA";
}
.fa-tree:before {
  content: "\F1BB";
}
.fa-spotify:before {
  content: "\F1BC";
}
.fa-deviantart:before {
  content: "\F1BD";
}
.fa-soundcloud:before {
  content: "\F1BE";
}
.fa-database:before {
  content: "\F1C0";
}
.fa-file-pdf-o:before {
  content: "\F1C1";
}
.fa-file-word-o:before {
  content: "\F1C2";
}
.fa-file-excel-o:before {
  content: "\F1C3";
}
.fa-file-powerpoint-o:before {
  content: "\F1C4";
}
.fa-file-image-o:before,
.fa-file-photo-o:before,
.fa-file-picture-o:before {
  content: "\F1C5";
}
.fa-file-archive-o:before,
.fa-file-zip-o:before {
  content: "\F1C6";
}
.fa-file-audio-o:before,
.fa-file-sound-o:before {
  content: "\F1C7";
}
.fa-file-movie-o:before,
.fa-file-video-o:before {
  content: "\F1C8";
}
.fa-file-code-o:before {
  content: "\F1C9";
}
.fa-vine:before {
  content: "\F1CA";
}
.fa-codepen:before {
  content: "\F1CB";
}
.fa-jsfiddle:before {
  content: "\F1CC";
}
.fa-life-bouy:before,
.fa-life-buoy:before,
.fa-life-ring:before,
.fa-life-saver:before,
.fa-support:before {
  content: "\F1CD";
}
.fa-circle-o-notch:before {
  content: "\F1CE";
}
.fa-ra:before,
.fa-rebel:before,
.fa-resistance:before {
  content: "\F1D0";
}
.fa-empire:before,
.fa-ge:before {
  content: "\F1D1";
}
.fa-git-square:before {
  content: "\F1D2";
}
.fa-git:before {
  content: "\F1D3";
}
.fa-hacker-news:before,
.fa-y-combinator-square:before,
.fa-yc-square:before {
  content: "\F1D4";
}
.fa-tencent-weibo:before {
  content: "\F1D5";
}
.fa-qq:before {
  content: "\F1D6";
}
.fa-wechat:before,
.fa-weixin:before {
  content: "\F1D7";
}
.fa-paper-plane:before,
.fa-send:before {
  content: "\F1D8";
}
.fa-paper-plane-o:before,
.fa-send-o:before {
  content: "\F1D9";
}
.fa-history:before {
  content: "\F1DA";
}
.fa-circle-thin:before {
  content: "\F1DB";
}
.fa-header:before {
  content: "\F1DC";
}
.fa-paragraph:before {
  content: "\F1DD";
}
.fa-sliders:before {
  content: "\F1DE";
}
.fa-share-alt:before {
  content: "\F1E0";
}
.fa-share-alt-square:before {
  content: "\F1E1";
}
.fa-bomb:before {
  content: "\F1E2";
}
.fa-futbol-o:before,
.fa-soccer-ball-o:before {
  content: "\F1E3";
}
.fa-tty:before {
  content: "\F1E4";
}
.fa-binoculars:before {
  content: "\F1E5";
}
.fa-plug:before {
  content: "\F1E6";
}
.fa-slideshare:before {
  content: "\F1E7";
}
.fa-twitch:before {
  content: "\F1E8";
}
.fa-yelp:before {
  content: "\F1E9";
}
.fa-newspaper-o:before {
  content: "\F1EA";
}
.fa-wifi:before {
  content: "\F1EB";
}
.fa-calculator:before {
  content: "\F1EC";
}
.fa-paypal:before {
  content: "\F1ED";
}
.fa-google-wallet:before {
  content: "\F1EE";
}
.fa-cc-visa:before {
  content: "\F1F0";
}
.fa-cc-mastercard:before {
  content: "\F1F1";
}
.fa-cc-discover:before {
  content: "\F1F2";
}
.fa-cc-amex:before {
  content: "\F1F3";
}
.fa-cc-paypal:before {
  content: "\F1F4";
}
.fa-cc-stripe:before {
  content: "\F1F5";
}
.fa-bell-slash:before {
  content: "\F1F6";
}
.fa-bell-slash-o:before {
  content: "\F1F7";
}
.fa-trash:before {
  content: "\F1F8";
}
.fa-copyright:before {
  content: "\F1F9";
}
.fa-at:before {
  content: "\F1FA";
}
.fa-eyedropper:before {
  content: "\F1FB";
}
.fa-paint-brush:before {
  content: "\F1FC";
}
.fa-birthday-cake:before {
  content: "\F1FD";
}
.fa-area-chart:before {
  content: "\F1FE";
}
.fa-pie-chart:before {
  content: "\F200";
}
.fa-line-chart:before {
  content: "\F201";
}
.fa-lastfm:before {
  content: "\F202";
}
.fa-lastfm-square:before {
  content: "\F203";
}
.fa-toggle-off:before {
  content: "\F204";
}
.fa-toggle-on:before {
  content: "\F205";
}
.fa-bicycle:before {
  content: "\F206";
}
.fa-bus:before {
  content: "\F207";
}
.fa-ioxhost:before {
  content: "\F208";
}
.fa-angellist:before {
  content: "\F209";
}
.fa-cc:before {
  content: "\F20A";
}
.fa-ils:before,
.fa-shekel:before,
.fa-sheqel:before {
  content: "\F20B";
}
.fa-meanpath:before {
  content: "\F20C";
}
.fa-buysellads:before {
  content: "\F20D";
}
.fa-connectdevelop:before {
  content: "\F20E";
}
.fa-dashcube:before {
  content: "\F210";
}
.fa-forumbee:before {
  content: "\F211";
}
.fa-leanpub:before {
  content: "\F212";
}
.fa-sellsy:before {
  content: "\F213";
}
.fa-shirtsinbulk:before {
  content: "\F214";
}
.fa-simplybuilt:before {
  content: "\F215";
}
.fa-skyatlas:before {
  content: "\F216";
}
.fa-cart-plus:before {
  content: "\F217";
}
.fa-cart-arrow-down:before {
  content: "\F218";
}
.fa-diamond:before {
  content: "\F219";
}
.fa-ship:before {
  content: "\F21A";
}
.fa-user-secret:before {
  content: "\F21B";
}
.fa-motorcycle:before {
  content: "\F21C";
}
.fa-street-view:before {
  content: "\F21D";
}
.fa-heartbeat:before {
  content: "\F21E";
}
.fa-venus:before {
  content: "\F221";
}
.fa-mars:before {
  content: "\F222";
}
.fa-mercury:before {
  content: "\F223";
}
.fa-intersex:before,
.fa-transgender:before {
  content: "\F224";
}
.fa-transgender-alt:before {
  content: "\F225";
}
.fa-venus-double:before {
  content: "\F226";
}
.fa-mars-double:before {
  content: "\F227";
}
.fa-venus-mars:before {
  content: "\F228";
}
.fa-mars-stroke:before {
  content: "\F229";
}
.fa-mars-stroke-v:before {
  content: "\F22A";
}
.fa-mars-stroke-h:before {
  content: "\F22B";
}
.fa-neuter:before {
  content: "\F22C";
}
.fa-genderless:before {
  content: "\F22D";
}
.fa-facebook-official:before {
  content: "\F230";
}
.fa-pinterest-p:before {
  content: "\F231";
}
.fa-whatsapp:before {
  content: "\F232";
}
.fa-server:before {
  content: "\F233";
}
.fa-user-plus:before {
  content: "\F234";
}
.fa-user-times:before {
  content: "\F235";
}
.fa-bed:before,
.fa-hotel:before {
  content: "\F236";
}
.fa-viacoin:before {
  content: "\F237";
}
.fa-train:before {
  content: "\F238";
}
.fa-subway:before {
  content: "\F239";
}
.fa-medium:before {
  content: "\F23A";
}
.fa-y-combinator:before,
.fa-yc:before {
  content: "\F23B";
}
.fa-optin-monster:before {
  content: "\F23C";
}
.fa-opencart:before {
  content: "\F23D";
}
.fa-expeditedssl:before {
  content: "\F23E";
}
.fa-battery-4:before,
.fa-battery-full:before,
.fa-battery:before {
  content: "\F240";
}
.fa-battery-3:before,
.fa-battery-three-quarters:before {
  content: "\F241";
}
.fa-battery-2:before,
.fa-battery-half:before {
  content: "\F242";
}
.fa-battery-1:before,
.fa-battery-quarter:before {
  content: "\F243";
}
.fa-battery-0:before,
.fa-battery-empty:before {
  content: "\F244";
}
.fa-mouse-pointer:before {
  content: "\F245";
}
.fa-i-cursor:before {
  content: "\F246";
}
.fa-object-group:before {
  content: "\F247";
}
.fa-object-ungroup:before {
  content: "\F248";
}
.fa-sticky-note:before {
  content: "\F249";
}
.fa-sticky-note-o:before {
  content: "\F24A";
}
.fa-cc-jcb:before {
  content: "\F24B";
}
.fa-cc-diners-club:before {
  content: "\F24C";
}
.fa-clone:before {
  content: "\F24D";
}
.fa-balance-scale:before {
  content: "\F24E";
}
.fa-hourglass-o:before {
  content: "\F250";
}
.fa-hourglass-1:before,
.fa-hourglass-start:before {
  content: "\F251";
}
.fa-hourglass-2:before,
.fa-hourglass-half:before {
  content: "\F252";
}
.fa-hourglass-3:before,
.fa-hourglass-end:before {
  content: "\F253";
}
.fa-hourglass:before {
  content: "\F254";
}
.fa-hand-grab-o:before,
.fa-hand-rock-o:before {
  content: "\F255";
}
.fa-hand-paper-o:before,
.fa-hand-stop-o:before {
  content: "\F256";
}
.fa-hand-scissors-o:before {
  content: "\F257";
}
.fa-hand-lizard-o:before {
  content: "\F258";
}
.fa-hand-spock-o:before {
  content: "\F259";
}
.fa-hand-pointer-o:before {
  content: "\F25A";
}
.fa-hand-peace-o:before {
  content: "\F25B";
}
.fa-trademark:before {
  content: "\F25C";
}
.fa-registered:before {
  content: "\F25D";
}
.fa-creative-commons:before {
  content: "\F25E";
}
.fa-gg:before {
  content: "\F260";
}
.fa-gg-circle:before {
  content: "\F261";
}
.fa-tripadvisor:before {
  content: "\F262";
}
.fa-odnoklassniki:before {
  content: "\F263";
}
.fa-odnoklassniki-square:before {
  content: "\F264";
}
.fa-get-pocket:before {
  content: "\F265";
}
.fa-wikipedia-w:before {
  content: "\F266";
}
.fa-safari:before {
  content: "\F267";
}
.fa-chrome:before {
  content: "\F268";
}
.fa-firefox:before {
  content: "\F269";
}
.fa-opera:before {
  content: "\F26A";
}
.fa-internet-explorer:before {
  content: "\F26B";
}
.fa-television:before,
.fa-tv:before {
  content: "\F26C";
}
.fa-contao:before {
  content: "\F26D";
}
.fa-500px:before {
  content: "\F26E";
}
.fa-amazon:before {
  content: "\F270";
}
.fa-calendar-plus-o:before {
  content: "\F271";
}
.fa-calendar-minus-o:before {
  content: "\F272";
}
.fa-calendar-times-o:before {
  content: "\F273";
}
.fa-calendar-check-o:before {
  content: "\F274";
}
.fa-industry:before {
  content: "\F275";
}
.fa-map-pin:before {
  content: "\F276";
}
.fa-map-signs:before {
  content: "\F277";
}
.fa-map-o:before {
  content: "\F278";
}
.fa-map:before {
  content: "\F279";
}
.fa-commenting:before {
  content: "\F27A";
}
.fa-commenting-o:before {
  content: "\F27B";
}
.fa-houzz:before {
  content: "\F27C";
}
.fa-vimeo:before {
  content: "\F27D";
}
.fa-black-tie:before {
  content: "\F27E";
}
.fa-fonticons:before {
  content: "\F280";
}
.fa-reddit-alien:before {
  content: "\F281";
}
.fa-edge:before {
  content: "\F282";
}
.fa-credit-card-alt:before {
  content: "\F283";
}
.fa-codiepie:before {
  content: "\F284";
}
.fa-modx:before {
  content: "\F285";
}
.fa-fort-awesome:before {
  content: "\F286";
}
.fa-usb:before {
  content: "\F287";
}
.fa-product-hunt:before {
  content: "\F288";
}
.fa-mixcloud:before {
  content: "\F289";
}
.fa-scribd:before {
  content: "\F28A";
}
.fa-pause-circle:before {
  content: "\F28B";
}
.fa-pause-circle-o:before {
  content: "\F28C";
}
.fa-stop-circle:before {
  content: "\F28D";
}
.fa-stop-circle-o:before {
  content: "\F28E";
}
.fa-shopping-bag:before {
  content: "\F290";
}
.fa-shopping-basket:before {
  content: "\F291";
}
.fa-hashtag:before {
  content: "\F292";
}
.fa-bluetooth:before {
  content: "\F293";
}
.fa-bluetooth-b:before {
  content: "\F294";
}
.fa-percent:before {
  content: "\F295";
}
.fa-gitlab:before {
  content: "\F296";
}
.fa-wpbeginner:before {
  content: "\F297";
}
.fa-wpforms:before {
  content: "\F298";
}
.fa-envira:before {
  content: "\F299";
}
.fa-universal-access:before {
  content: "\F29A";
}
.fa-wheelchair-alt:before {
  content: "\F29B";
}
.fa-question-circle-o:before {
  content: "\F29C";
}
.fa-blind:before {
  content: "\F29D";
}
.fa-audio-description:before {
  content: "\F29E";
}
.fa-volume-control-phone:before {
  content: "\F2A0";
}
.fa-braille:before {
  content: "\F2A1";
}
.fa-assistive-listening-systems:before {
  content: "\F2A2";
}
.fa-american-sign-language-interpreting:before,
.fa-asl-interpreting:before {
  content: "\F2A3";
}
.fa-deaf:before,
.fa-deafness:before,
.fa-hard-of-hearing:before {
  content: "\F2A4";
}
.fa-glide:before {
  content: "\F2A5";
}
.fa-glide-g:before {
  content: "\F2A6";
}
.fa-sign-language:before,
.fa-signing:before {
  content: "\F2A7";
}
.fa-low-vision:before {
  content: "\F2A8";
}
.fa-viadeo:before {
  content: "\F2A9";
}
.fa-viadeo-square:before {
  content: "\F2AA";
}
.fa-snapchat:before {
  content: "\F2AB";
}
.fa-snapchat-ghost:before {
  content: "\F2AC";
}
.fa-snapchat-square:before {
  content: "\F2AD";
}
.fa-pied-piper:before {
  content: "\F2AE";
}
.fa-first-order:before {
  content: "\F2B0";
}
.fa-yoast:before {
  content: "\F2B1";
}
.fa-themeisle:before {
  content: "\F2B2";
}
.fa-google-plus-circle:before,
.fa-google-plus-official:before {
  content: "\F2B3";
}
.fa-fa:before,
.fa-font-awesome:before {
  content: "\F2B4";
}
.fa-handshake-o:before {
  content: "\F2B5";
}
.fa-envelope-open:before {
  content: "\F2B6";
}
.fa-envelope-open-o:before {
  content: "\F2B7";
}
.fa-linode:before {
  content: "\F2B8";
}
.fa-address-book:before {
  content: "\F2B9";
}
.fa-address-book-o:before {
  content: "\F2BA";
}
.fa-address-card:before,
.fa-vcard:before {
  content: "\F2BB";
}
.fa-address-card-o:before,
.fa-vcard-o:before {
  content: "\F2BC";
}
.fa-user-circle:before {
  content: "\F2BD";
}
.fa-user-circle-o:before {
  content: "\F2BE";
}
.fa-user-o:before {
  content: "\F2C0";
}
.fa-id-badge:before {
  content: "\F2C1";
}
.fa-drivers-license:before,
.fa-id-card:before {
  content: "\F2C2";
}
.fa-drivers-license-o:before,
.fa-id-card-o:before {
  content: "\F2C3";
}
.fa-quora:before {
  content: "\F2C4";
}
.fa-free-code-camp:before {
  content: "\F2C5";
}
.fa-telegram:before {
  content: "\F2C6";
}
.fa-thermometer-4:before,
.fa-thermometer-full:before,
.fa-thermometer:before {
  content: "\F2C7";
}
.fa-thermometer-3:before,
.fa-thermometer-three-quarters:before {
  content: "\F2C8";
}
.fa-thermometer-2:before,
.fa-thermometer-half:before {
  content: "\F2C9";
}
.fa-thermometer-1:before,
.fa-thermometer-quarter:before {
  content: "\F2CA";
}
.fa-thermometer-0:before,
.fa-thermometer-empty:before {
  content: "\F2CB";
}
.fa-shower:before {
  content: "\F2CC";
}
.fa-bath:before,
.fa-bathtub:before,
.fa-s15:before {
  content: "\F2CD";
}
.fa-podcast:before {
  content: "\F2CE";
}
.fa-window-maximize:before {
  content: "\F2D0";
}
.fa-window-minimize:before {
  content: "\F2D1";
}
.fa-window-restore:before {
  content: "\F2D2";
}
.fa-times-rectangle:before,
.fa-window-close:before {
  content: "\F2D3";
}
.fa-times-rectangle-o:before,
.fa-window-close-o:before {
  content: "\F2D4";
}
.fa-bandcamp:before {
  content: "\F2D5";
}
.fa-grav:before {
  content: "\F2D6";
}
.fa-etsy:before {
  content: "\F2D7";
}
.fa-imdb:before {
  content: "\F2D8";
}
.fa-ravelry:before {
  content: "\F2D9";
}
.fa-eercast:before {
  content: "\F2DA";
}
.fa-microchip:before {
  content: "\F2DB";
}
.fa-snowflake-o:before {
  content: "\F2DC";
}
.fa-superpowers:before {
  content: "\F2DD";
}
.fa-wpexplorer:before {
  content: "\F2DE";
}
.fa-meetup:before {
  content: "\F2E0";
}
.fa-copy:before {
  content: "\F0C5";
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
[dir] .sr-only {
  padding: 0;
  margin: -1px;
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
}
[dir] .sr-only-focusable:active,
[dir] .sr-only-focusable:focus {
  margin: 0;
}
@font-face {
  font-family: icons;
  src: url(/store-front/fonts/icons.eot?afvdts);
  src: url(/store-front/fonts/icons.eot?afvdts#iefix)
      format("embedded-opentype"),
    url(/store-front/fonts/icons.ttf?afvdts) format("truetype"),
    url(/store-front/fonts/icons.woff?afvdts) format("woff"),
    url(/store-front/fonts/icons.svg?afvdts#icons) format("svg");
  font-weight: 400;
  font-style: normal;
  font-display: block;
}
.yc {
  font-family: icons;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.yc-alert-circle:before {
  content: "\E900";
}
.yc-alert-triangle:before {
  content: "\E901";
}
.yc-check:before {
  content: "\E902";
}
.yc-check-circle:before {
  content: "\E91C";
}
.yc-chevron-down:before {
  content: "\E903";
}
.yc-chevron-left:before {
  content: "\E904";
}
.yc-chevron-right:before {
  content: "\E905";
}
.yc-chevron-up:before {
  content: "\E906";
}
.yc-credit-card:before {
  content: "\E907";
}
.yc-edit:before {
  content: "\E908";
}
.yc-eye:before {
  content: "\E909";
}
.yc-eye-off:before {
  content: "\E90A";
}
.yc-facebook:before {
  content: "\E90B";
}
.yc-folder:before {
  content: "\E90C";
}
.yc-home:before {
  content: "\E90D";
}
.yc-instagram:before {
  content: "\E90E";
}
.yc-loader:before {
  content: "\E90F";
}
.yc-lock:before {
  content: "\E910";
}
.yc-mail:before {
  content: "\E911";
}
.yc-menu:before {
  content: "\E91D";
}
.yc-minus:before {
  content: "\E912";
}
.yc-phone:before {
  content: "\E91E";
}
.yc-plus:before {
  content: "\E913";
}
.yc-power:before {
  content: "\E914";
}
.yc-search:before {
  content: "\E915";
}
.yc-shopping-cart:before {
  content: "\E916";
}
.yc-trash:before {
  content: "\E917";
}
.yc-twitter:before {
  content: "\E918";
}
.yc-upload:before {
  content: "\E919";
}
.yc-user:before {
  content: "\E91A";
}
.yc-x-circle:before {
  content: "\E91B";
}
.yc-tiktok:before {
  content: "\E91F";
}
.container {
  width: 1124px;
}
[dir] .container {
  margin: 0 auto;
}
@media (max-width: 1124px) {
  .container {
    width: 100%;
  }
  [dir] .container {
    padding: 0 15px;
  }
}
.is-grid {
  display: grid;
  grid-gap: 25px;
}
.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}
.grid-5 {
  grid-template-columns: repeat(5, 1fr);
}
.list-unstyled {
  list-style: none;
}
[dir] .list-unstyled {
  margin: 0;
  padding: 0;
}
.search-input {
  position: relative;
}
.search-input button,
.search-input input[type="submit"] {
  position: absolute;
  top: 0;
  width: 50px;
  height: 100%;
  line-height: 100%;
  font-size: 18px;
}
[dir] .search-input button,
[dir] .search-input input[type="submit"] {
  text-align: center;
}
[dir="ltr"] .search-input button,
[dir="ltr"] .search-input input[type="submit"] {
  right: 0;
}
[dir="rtl"] .search-input button,
[dir="rtl"] .search-input input[type="submit"] {
  left: 0;
}
[dir] .app-heading {
  text-align: center;
  margin: 0 0 34px;
}
.app-heading .heading-primary {
  font-size: 26px;
  line-height: 36px;
}
.app-heading .heading-description {
  color: #696969;
}
[dir] .app-heading .heading-description {
  margin: 5px 0 0;
}
@media (max-width: 768px) {
  [dir] .app-heading {
    margin: 0 0 17px;
  }
  .app-heading .heading-primary {
    font-size: 22px;
    line-height: 32px;
  }
}
.currency-value {
  display: inline-flex;
  align-items: center;
}
.currency-value .currency,
.currency-value .value,
.currency-value small {
  font-size: inherit;
}
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .load-more {
  margin: 30px 0 0;
}
.load-more .button {
  text-transform: uppercase;
}
[dir] .load-more .button {
  padding: 8px 34px 9px;
}
.share-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (min-width: 425px) {
  [dir="ltr"] .share-box .share:not(:last-child) {
    margin: 0 10px 0 0;
  }
  [dir="rtl"] .share-box .share:not(:last-child) {
    margin: 0 0 0 10px;
  }
}
.share-box .share a {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  width: 150px;
  color: #fff;
}
.share-box .share a .fa,
.share-box .share a .yc {
  display: block;
  font-size: 18px;
}
[dir="ltr"] .share-box .share a .fa,
[dir="ltr"] .share-box .share a .yc {
  margin: 0 8px 0 0;
}
[dir="rtl"] .share-box .share a .fa,
[dir="rtl"] .share-box .share a .yc {
  margin: 0 0 0 8px;
}
@media (max-width: 425px) {
  .share-box .share a .fa,
  .share-box .share a .yc {
    display: none;
  }
}
@media (max-width: 425px) {
  .share-box .share,
  .share-box .share a {
    width: 100%;
  }
}
[dir] .share-box .share.facebook a {
  background-color: #4267b1;
}
[dir] .share-box .share.twitter a {
  background-color: #4cb3f4;
}
[dir] .share-box .share.whatsapp a {
  background-color: #25d366;
}
@media (min-width: 768px) {
  .share-box .share.whatsapp {
    display: none;
  }
}
.is-loading {
  position: relative;
  opacity: 0.6;
  pointer-events: none;
}
.is-loading:before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-family: icons;
  content: "\E90F";
  font-size: 20px;
  color: #1a1a1a;
  z-index: 1;
}
[dir="ltr"] .is-loading:before {
  -webkit-animation: fa-spin-ltr 2s linear infinite;
  animation: fa-spin-ltr 2s linear infinite;
}
[dir="rtl"] .is-loading:before {
  -webkit-animation: fa-spin-rtl 2s linear infinite;
  animation: fa-spin-rtl 2s linear infinite;
}
.is-loading:after {
  content: "";
  top: 0;
  width: 100%;
  height: 100%;
}
[dir] .is-loading:after {
  background-color: hsla(0, 0%, 100%, 0.8);
}
[dir="ltr"] .is-loading:after {
  left: 0;
}
[dir="rtl"] .is-loading:after {
  right: 0;
}
.is-loading:after,
.is-loading:before {
  position: absolute;
}
[dir] .is-loading.button {
  border: 0;
}
[dir] .lazy,
[dir] a.lazy {
  background-color: #fafafa;
}
.lazy {
  position: relative;
}
.lazy img {
  opacity: 0;
}
.lazy:after,
.lazy:before {
  content: "";
  position: absolute;
  top: 50%;
  display: block;
  width: 60px;
  height: 60px;
  opacity: 0;
  z-index: 1;
}
[dir] .lazy:after,
[dir] .lazy:before {
  background-color: #bbb;
  border: 2px solid #bbb;
  border-radius: 50%;
}
[dir="ltr"] .lazy:after,
[dir="ltr"] .lazy:before {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .lazy:after,
[dir="rtl"] .lazy:before {
  right: 50%;
  transform: translate(50%, -50%);
}
[dir="ltr"] .lazy:before {
  -webkit-animation: lazyloading-ltr 2s linear 0s infinite;
  animation: lazyloading-ltr 2s linear 0s infinite;
}
[dir="rtl"] .lazy:before {
  -webkit-animation: lazyloading-rtl 2s linear 0s infinite;
  animation: lazyloading-rtl 2s linear 0s infinite;
}
[dir="ltr"] .lazy:after {
  -webkit-animation: lazyloading-ltr 2s linear -1s infinite;
  animation: lazyloading-ltr 2s linear -1s infinite;
}
[dir="rtl"] .lazy:after {
  -webkit-animation: lazyloading-rtl 2s linear -1s infinite;
  animation: lazyloading-rtl 2s linear -1s infinite;
}
@-webkit-keyframes lazyloading-ltr {
  0% {
    background-color: hsla(0, 0%, 68.6%, 0.95);
    transform: translate(-50%, -50%) scale(0.1);
    opacity: 1;
  }
  60% {
    background-color: hsla(0, 0%, 68.6%, 0.5);
  }
  to {
    background-color: hsla(0, 0%, 68.6%, 0);
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}
@keyframes lazyloading-ltr {
  0% {
    background-color: hsla(0, 0%, 68.6%, 0.95);
    transform: translate(-50%, -50%) scale(0.1);
    opacity: 1;
  }
  60% {
    background-color: hsla(0, 0%, 68.6%, 0.5);
  }
  to {
    background-color: hsla(0, 0%, 68.6%, 0);
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}
@-webkit-keyframes lazyloading-rtl {
  0% {
    background-color: hsla(0, 0%, 68.6%, 0.95);
    transform: translate(50%, -50%) scale(0.1);
    opacity: 1;
  }
  60% {
    background-color: hsla(0, 0%, 68.6%, 0.5);
  }
  to {
    background-color: hsla(0, 0%, 68.6%, 0);
    transform: translate(50%, -50%) scale(1);
    opacity: 0;
  }
}
@keyframes lazyloading-rtl {
  0% {
    background-color: hsla(0, 0%, 68.6%, 0.95);
    transform: translate(50%, -50%) scale(0.1);
    opacity: 1;
  }
  60% {
    background-color: hsla(0, 0%, 68.6%, 0.5);
  }
  to {
    background-color: hsla(0, 0%, 68.6%, 0);
    transform: translate(50%, -50%) scale(1);
    opacity: 0;
  }
}
@media (min-width: 768px) {
  [dir] .auth-section,
  [dir] .cart-section,
  [dir] .categories-section,
  [dir] .orders-section,
  [dir] .pages-section,
  [dir] .section-search,
  [dir] .single-category,
  [dir] .upsell-section {
    padding: 60px 0;
  }
}
@media (max-width: 768px) {
  [dir] .auth-section,
  [dir] .cart-section,
  [dir] .categories-section,
  [dir] .orders-section,
  [dir] .pages-section,
  [dir] .section-search,
  [dir] .single-category,
  [dir] .upsell-section {
    padding: 30px 0;
  }
}
[dir] .notice-bar {
  padding: 15px 0;
}
.notice-bar .fr-view {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
[dir] .notice-bar .fr-view p {
  margin-bottom: 0;
}
[dir] .notice-bar .fr-view p:not(:last-child) {
  margin-bottom: 1rem;
}
.notice-bar.mobile-notice-bar {
  display: none;
}
@media (max-width: 1124px) {
  .notice-bar.mobile-notice-bar {
    display: block;
  }
  .notice-bar.desktop-notice-bar {
    display: none;
  }
}
.toast {
  position: fixed;
  bottom: 0;
  width: 100%;
  transition: transform 0.25s;
  z-index: 4;
}
[dir] .toast {
  transform: translateY(100%);
}
[dir="ltr"] .toast {
  left: 0;
}
[dir="rtl"] .toast {
  right: 0;
}
[dir] .toast.is-visible {
  transform: translateY(0);
}
.alert {
  color: #fff;
  min-height: 50px;
}
.alert .container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
}
[dir] .alert .container {
  text-align: center;
}
.alert .alert-icon {
  display: block;
  width: 30px;
  height: 30px;
  line-height: 30px;
}
[dir] .alert .alert-icon {
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
}
[dir="ltr"] .alert .alert-icon {
  margin: 0 8px 0 0;
}
[dir="rtl"] .alert .alert-icon {
  margin: 0 0 0 8px;
}
.alert .alert-icon:before {
  font-family: icons;
  font-size: 18px;
}
[dir] .alert .alert-icon:before {
  text-shadow: none;
}
[dir="ltr"] .alert .alert-icon:before {
  padding: 0 0 0 1px;
}
[dir="rtl"] .alert .alert-icon:before {
  padding: 0 1px 0 0;
}
.alert .alert-body {
  display: flex;
  align-items: center;
}
[dir] .alert .alert-message p {
  margin: 0;
}
@media (max-width: 425px) {
  [dir] .alert {
    padding: 5px 0;
  }
  .alert .container {
    flex-direction: column;
  }
  [dir] .alert .alert-icon {
    margin: 0 0 3px;
  }
}
[dir] .alert-success {
  background-color: var(--success-color);
}
.alert-success .alert-icon:before {
  content: "\E91C";
  color: var(--success-color);
}
[dir] .alert-success .alert-icon:before {
  padding: 0;
}
[dir] .alert-info {
  background-color: var(--info-color);
}
.alert-icon:before {
  content: "\E900";
  color: var(--info-color);
}
[dir] .alert-warning {
  background-color: var(--warning-color);
}
.alert-warning .alert-icon:before {
  content: "\E901";
  color: var(--warning-color);
}
[dir] .alert-danger {
  background-color: var(--danger-color);
}
.alert-danger .alert-icon:before {
  content: "\E91B";
  color: var(--danger-color);
}
[dir] .breadcrumb {
  border-bottom: 1px solid #f0f0f0;
}
.breadcrumb .breadcrumb-list {
  height: 55px;
}
.breadcrumb .breadcrumb-list,
.breadcrumb .breadcrumb-list li {
  display: flex;
  align-items: center;
}
.breadcrumb .breadcrumb-list li:not(:last-child):after {
  content: "\E905";
  display: block;
  font-family: icons;
  font-size: 10px;
  line-height: 1;
}
[dir] .breadcrumb .breadcrumb-list li:not(:last-child):after {
  margin: 0 10px;
}
@media (max-width: 425px) {
  [dir] .breadcrumb .breadcrumb-list li:not(:last-child):after {
    margin: 0 5px;
  }
}
.breadcrumb .breadcrumb-list li:first-child a {
  font-size: 0;
}
.breadcrumb .breadcrumb-list li:first-child a:before {
  content: "\E90D";
  font-family: icons;
  font-size: 14px;
}
.breadcrumb .breadcrumb-list a {
  transition: color 0.25s;
}
.breadcrumb .breadcrumb-list a:hover {
  color: var(--primary-color);
}
.breadcrumb .breadcrumb-list a,
.breadcrumb .breadcrumb-list span {
  font-size: 12px;
  color: #454545;
  text-transform: lowercase;
}
.breadcrumb .breadcrumb-list a:first-letter,
.breadcrumb .breadcrumb-list span:first-letter {
  text-transform: capitalize;
}
@media (max-width: 425px) {
  .breadcrumb .breadcrumb-list a,
  .breadcrumb .breadcrumb-list span {
    font-size: 12px;
  }
}
.breadcrumb .breadcrumb-list span {
  color: #737373;
}
[dir] .topbar {
  border-bottom: 1px solid #f0f0f0;
}
.topbar .container {
  justify-content: space-between;
}
.topbar .container,
.topbar .topbar-list {
  display: flex;
  align-items: center;
}
.topbar .topbar-list a,
.topbar .topbar-list button {
  display: block;
  color: #747474;
  font-size: 13px;
  transition: color 0.25s;
}
[dir] .topbar .topbar-list a,
[dir] .topbar .topbar-list button {
  padding: 12px 7px;
}
.topbar .topbar-list a:hover,
.topbar .topbar-list button:hover {
  color: var(--primary-color);
}
.topbar .logout-list {
  display: flex;
}
.topbar .logout-list a {
  text-transform: capitalize;
  color: var(--primary-color);
}
[dir] .topbar .logout-list .logout-button {
  padding: 14px 0;
  margin: 0 0 1px;
}
@media (max-width: 425px) {
  .topbar {
    display: none;
  }
}
.header-wrapper {
  display: flex;
  flex-direction: column;
}
@media (min-width: 768px) {
  .header-wrapper.notice-down-desktop {
    flex-direction: column-reverse;
  }
}
@media (max-width: 768px) {
  .header-wrapper.notice-down-mobile {
    flex-direction: column-reverse;
  }
}
[dir] .header-wrapper.fixed-header {
  margin: 100px 0 0;
}
@media (max-width: 1124px) {
  [dir] .header-wrapper.fixed-header {
    margin: 60px 0 0;
  }
}
.header-wrapper.fixed-header .app-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 2;
}
[dir="ltr"] .header-wrapper.fixed-header .app-header,
[dir="rtl"] .header-wrapper.fixed-header .app-header {
  -webkit-animation: slide-down 0.7s;
  animation: slide-down 0.7s;
}
.header-container .app-header {
  position: relative;
}
[dir] .header-container .app-header {
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
}
.header-container .app-header.navigation-active .overlay {
  opacity: 1;
  visibility: visible;
}
[dir] .header-container .app-header.navigation-active .side-navigation {
  transform: translateZ(0);
}
.header-container .app-header.search-active .search-form {
  opacity: 1;
  visibility: visible;
}
[dir] .header-container .app-header.search-active .search-form {
  transform: translateY(0);
}
.main-bar {
  position: relative;
  z-index: 3;
}
[dir] .main-bar {
  border-bottom: 1px solid #f0f0f0;
}
.main-bar .container {
  position: relative;
  height: 100%;
}
.desktop-bar {
  height: 100px;
}
@media (max-width: 1124px) {
  .desktop-bar {
    display: none;
  }
}
.desktop-bar .header-brand {
  max-width: 170px;
  height: 85px;
}
[dir="ltr"] .desktop-bar .header-element:not(:last-child) {
  margin-right: 10px;
}
[dir="rtl"] .desktop-bar .header-element:not(:last-child) {
  margin-left: 10px;
}
.mobile-bar {
  display: none;
  height: 60px;
}
@media (max-width: 1124px) {
  .mobile-bar {
    display: block;
  }
}
.mobile-bar .header-brand {
  max-width: 120px;
  height: 55px;
}
[dir="ltr"] .mobile-bar .header-left {
  left: 15px;
}
[dir="ltr"] .mobile-bar .header-right,
[dir="rtl"] .mobile-bar .header-left {
  right: 15px;
}
[dir="rtl"] .mobile-bar .header-right {
  left: 15px;
}
.header-center,
.header-left,
.header-right {
  position: absolute;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
[dir="ltr"] .header-left {
  left: 0;
}
[dir="rtl"] .header-left {
  right: 0;
}
.header-center {
  width: 100%;
}
@media (max-width: 1124px) {
  .header-center {
    width: calc(100% - 30px);
  }
}
[dir="ltr"] .header-right {
  right: 0;
}
[dir="rtl"] .header-right {
  left: 0;
}
.header-brand {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}
.header-brand img {
  max-height: 100%;
}
.header-switcher {
  display: block;
  position: relative;
  z-index: 1;
  font-size: 20px;
}
[dir] .header-switcher {
  text-align: center;
}
@media (max-width: 1124px) {
  .header-switcher {
    font-size: 22px;
  }
}
.header-list {
  display: flex;
  align-items: center;
}
.header-list a {
  display: block;
  position: relative;
  height: 100%;
  font-size: 13px;
  font-weight: 600;
  line-height: 100px;
  text-transform: capitalize;
  transition: color 0.25s;
  z-index: 1;
}
[dir] .header-list a {
  padding: 0 16px;
}
.header-list a:before {
  content: "";
  position: absolute;
  top: 0;
  width: 0;
  height: 3px;
  transition: width 0.25s;
}
[dir] .header-list a:before {
  background-color: var(--primary-color);
}
[dir="ltr"] .header-list a:before {
  left: 50%;
  transform: translateX(-50%);
}
[dir="rtl"] .header-list a:before {
  right: 50%;
  transform: translateX(50%);
}
.header-list a:hover {
  color: var(--primary-color);
}
[dir] .header-list a:hover {
  background-color: hsla(0, 0%, 97.6%, 0.5);
}
.header-list a:hover:before {
  width: 100%;
}
.side-cart-summary {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 4;
  width: 350px;
  transition: transform 0.25s;
}
[dir] .side-cart-summary {
  background-color: #fff;
}
[dir="ltr"] .side-cart-summary {
  right: 0;
  border-left: 1px solid #f0f0f0;
  transform: translateX(100%);
}
[dir="rtl"] .side-cart-summary {
  left: 0;
  border-right: 1px solid #f0f0f0;
  transform: translateX(-100%);
}
@media (max-width: 425px) {
  .side-cart-summary {
    width: 80%;
  }
}
.side-cart-summary .cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .side-cart-summary .cart-header {
  padding: 15px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}
.side-cart-summary .cart-header h3 {
  font-weight: 600;
}
[dir="ltr"] .side-cart-summary .cart-header small {
  margin: 0 0 0 5px;
}
[dir="rtl"] .side-cart-summary .cart-header small {
  margin: 0 5px 0 0;
}
.side-cart-summary .cart-header .yc {
  font-size: 20px;
}
[dir] .side-cart-summary .cart-header .yc {
  cursor: pointer;
}
@media (max-width: 1124px) {
  .side-cart-summary .cart-header .yc {
    font-size: 22px;
  }
}
.side-cart-summary .cart-body {
  height: calc(100% - 206px);
  overflow-y: auto;
}
[dir] .side-cart-summary .cart-body {
  padding: 15px;
}
.side-cart-summary .cart-body .cart-list .cart-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.side-cart-summary .cart-body .cart-list .cart-item .item-thumbnail {
  width: 60px;
}
[dir] .side-cart-summary .cart-body .cart-list .cart-item .item-thumbnail {
  border: 1px solid #f0f0f0;
}
.side-cart-summary .cart-body .cart-list .cart-item .item-body {
  display: flex;
  width: calc(100% - 60px);
}
[dir="ltr"] .side-cart-summary .cart-body .cart-list .cart-item .item-body {
  margin: 0 0 0 10px;
}
[dir="rtl"] .side-cart-summary .cart-body .cart-list .cart-item .item-body {
  margin: 0 10px 0 0;
}
.side-cart-summary .cart-body .cart-list .cart-item .item-body .item-details {
  width: calc(100% - 25px);
}
[dir="ltr"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details {
  padding: 0 10px 0 0;
}
[dir="rtl"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details {
  padding: 0 0 0 10px;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  h3 {
  font-size: 14px;
  font-weight: 600;
}
[dir]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  h3 {
  margin: 0 0 8px;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  h3
  a {
  color: inherit;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper {
  display: flex;
  align-items: center;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity {
  font-size: 14px;
  font-weight: 500;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  small {
  font-size: 13px;
  min-width: 20px;
  height: 18px;
  display: inline-block;
  line-height: 16px;
  font-weight: 600;
}
[dir]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  small {
  text-align: center;
  background-color: #fafafa;
  border-radius: 3px;
  border: 1px solid #e5e5e5;
  padding: 0 1px;
}
[dir="ltr"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  small {
  margin: 0 0 0 5px;
}
[dir="rtl"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  small {
  margin: 0 5px 0 0;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  input {
  width: 24px;
  height: 20px;
  font-size: 13px;
  font-weight: 600;
}
[dir]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .quantity
  input {
  text-align: center;
  padding: 0;
  margin: 0 5px;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .currency-value {
  font-size: 13px;
  font-weight: 600;
}
[dir="ltr"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .currency-value {
  margin: 0 0 0 10px;
}
[dir="rtl"]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-details
  .quantity-wrapper
  .currency-value {
  margin: 0 10px 0 0;
}
.side-cart-summary .cart-body .cart-list .cart-item .item-body .item-actions {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 25px;
  height: 50px;
}
[dir]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-actions {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-actions
  button {
  font-size: 18px;
}
[dir]
  .side-cart-summary
  .cart-body
  .cart-list
  .cart-item
  .item-body
  .item-actions
  button:not(:last-child) {
  margin: 0 0 5px;
}
[dir] .side-cart-summary .cart-body .cart-list .cart-item:not(:last-child) {
  padding-bottom: 20px;
}
.side-cart-summary .cart-body .cart-empty {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
[dir] .side-cart-summary .cart-body .cart-empty p {
  margin: 15px 0 0;
}
.side-cart-summary .cart-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
[dir] .side-cart-summary .cart-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
}
[dir] .side-cart-summary .cart-body::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}
.side-cart-summary .cart-footer {
  position: absolute;
  bottom: 0;
  width: 100%;
}
[dir] .side-cart-summary .cart-footer {
  padding: 15px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}
[dir="ltr"] .side-cart-summary .cart-footer {
  left: 0;
}
[dir="rtl"] .side-cart-summary .cart-footer {
  right: 0;
}
.side-cart-summary .cart-footer h4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}
.side-cart-summary .cart-footer .cart-actions {
  display: grid;
  grid-gap: 10px;
}
[dir] .side-cart-summary .cart-footer .cart-actions {
  margin: 15px 0 0;
}
[dir] .side-cart-summary.active {
  transform: translateX(0);
}
.cart-switcher {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .cart-switcher {
  cursor: pointer;
}
.cart-switcher .cart-icon {
  display: block;
  height: 100%;
  font-size: 20px;
}
[dir] .cart-switcher .cart-icon {
  text-align: center;
}
@media (max-width: 1124px) {
  .cart-switcher .cart-icon {
    font-size: 22px;
  }
}
.cart-switcher .currency-value {
  font-weight: 600;
}
[dir] .cart-switcher .currency-value {
  padding: 0 15px;
}
.cart-switcher .cart-count {
  position: absolute;
  font-size: 11px;
  font-weight: 500;
  color: #fff;
}
[dir] .cart-switcher .cart-count {
  background-color: var(--primary-color);
  border-radius: 50%;
  text-align: center;
}
.overlay {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  opacity: 0;
  z-index: 3;
  transition: visibility 0.25s, opacity 0.25s;
}
[dir] .overlay {
  background-color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
}
[dir="ltr"] .overlay {
  left: 0;
}
[dir="rtl"] .overlay {
  right: 0;
}
.side-navigation {
  position: fixed;
  top: 0;
  width: 340px;
  height: 100%;
  overflow-y: auto;
  z-index: 3;
  transition: transform 0.45s cubic-bezier(0.645, 0.045, 0.355, 1);
}
[dir] .side-navigation {
  background-color: #fff;
  padding: 20px 15px;
}
[dir="ltr"] .side-navigation {
  left: 0;
  transform: translate3d(-100%, 0, 0);
}
[dir="rtl"] .side-navigation {
  right: 0;
  transform: translate3d(100%, 0, 0);
}
.side-navigation .navigation-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 170px;
  height: 85px;
}
[dir] .side-navigation .navigation-brand {
  margin: 0 auto;
}
.side-navigation .navigation-brand img {
  max-height: 100%;
}
@media (max-width: 425px) {
  .side-navigation .navigation-brand {
    max-width: 120px;
    height: 55px;
  }
}
[dir] .side-navigation .navigation-search {
  margin: 20px 0;
}
[dir] .side-navigation .navigation-list {
  margin-bottom: 20px;
}
[dir] .side-navigation .navigation-list li {
  border-bottom: 1px solid #f0f0f0;
}
.side-navigation .navigation-list li a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 13px;
  text-transform: capitalize;
  transition: color 0.35s ease;
  color: inherit;
}
[dir] .side-navigation .navigation-list li a {
  padding: 15px 0;
}
.side-navigation .navigation-list li a:after {
  content: "\E905";
  font-family: icons;
  color: #747474;
  font-weight: 400;
}
.side-navigation .navigation-pages li a,
.side-navigation .navigation-pages li button {
  display: block;
  color: #747474;
  font-size: 12px;
  text-transform: capitalize;
  transition: color 0.35s;
}
.side-navigation .navigation-pages li a:hover,
.side-navigation .navigation-pages li button:hover {
  color: var(--primary-color);
}
[dir] .side-navigation .navigation-pages li:not(:last-child) {
  margin-bottom: 15px;
}
.side-navigation::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
[dir] .side-navigation::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
}
[dir] .side-navigation::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}
@media (max-width: 425px) {
  .side-navigation {
    width: 80%;
  }
}
.search-form {
  position: absolute;
  top: 100%;
  width: 100%;
  transition: transform 0.25s ease;
  visibility: hidden;
  opacity: 0;
  z-index: 2;
}
[dir] .search-form {
  background-color: var(--primary-color);
  padding: 5px;
  transform: translateY(-20px);
}
[dir="ltr"] .search-form {
  left: 0;
}
[dir="rtl"] .search-form {
  right: 0;
}
.search-form .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .search-form .container {
  padding: 0;
}
@media (max-width: 425px) {
  .search-form .container {
    flex-direction: column;
  }
}
.search-form .search-select {
  position: relative;
  height: 40px;
}
.search-form .search-select select {
  width: 100%;
  height: 100%;
  color: #fff;
}
[dir] .search-form .search-select select {
  background-color: rgba(0, 0, 0, 0.15);
}
.search-form .search-select:after {
  content: "\E903";
  font-family: icons;
  position: absolute;
  top: 50%;
  font-size: 12px;
  color: #fff;
  pointer-events: none;
}
[dir] .search-form .search-select:after {
  transform: translateY(-50%);
  cursor: pointer;
}
[dir="ltr"] .search-form .search-select:after {
  right: 15px;
}
[dir="rtl"] .search-form .search-select:after {
  left: 15px;
}
@media (min-width: 425px) {
  .search-form .search-select {
    width: 200px;
  }
  [dir="ltr"] .search-form .search-select {
    margin: 0 10px 0 0;
  }
  [dir="rtl"] .search-form .search-select {
    margin: 0 0 0 10px;
  }
}
@media (max-width: 425px) {
  .search-form .search-select {
    width: 100%;
  }
  [dir] .search-form .search-select {
    margin: 0 0 5px;
  }
}
.search-form .search-input input[type="search"] {
  height: 40px;
}
[dir] .search-form .search-input input[type="search"] {
  border: 0;
  background-color: #fff;
}
[dir="ltr"] .search-form .search-input input[type="search"] {
  padding: 10px 50px 10px 15px;
}
[dir="rtl"] .search-form .search-input input[type="search"] {
  padding: 10px 15px 10px 50px;
}
.search-form .search-input button {
  height: 100%;
}
@media (min-width: 425px) {
  .search-form .search-input {
    width: calc(100% - 200px);
  }
}
@media (max-width: 425px) {
  .search-form .search-input {
    width: 100%;
  }
}
@media (max-width: 1124px) {
  [dir="ltr"] .desktop-bar .header-left {
    left: 15px;
  }
  [dir="ltr"] .desktop-bar .header-right,
  [dir="rtl"] .desktop-bar .header-left {
    right: 15px;
  }
  [dir="rtl"] .desktop-bar .header-right {
    left: 15px;
  }
}
.desktop-bar .header-switcher {
  width: 40px;
  height: 40px;
  line-height: 40px;
}
.desktop-bar .cart-switcher {
  height: 40px;
}
[dir] .desktop-bar .cart-switcher {
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.desktop-bar .cart-switcher .cart-icon {
  width: 40px;
  line-height: 38px;
}
[dir="ltr"] .desktop-bar .cart-switcher .cart-icon {
  border-right: 1px solid #f0f0f0;
}
[dir="rtl"] .desktop-bar .cart-switcher .cart-icon {
  border-left: 1px solid #f0f0f0;
}
.desktop-bar .cart-switcher .cart-count {
  top: -11px;
  width: 22px;
  height: 22px;
  line-height: 22px;
}
[dir="ltr"] .desktop-bar .cart-switcher .cart-count {
  left: 28px;
}
[dir="rtl"] .desktop-bar .cart-switcher .cart-count {
  right: 28px;
}
.mobile-bar .cart-switcher {
  height: 30px;
}
.mobile-bar .cart-switcher .cart-icon {
  width: 30px;
  line-height: 30px;
}
.mobile-bar .cart-switcher .currency-value {
  display: none;
}
.mobile-bar .cart-switcher .cart-count {
  top: -8px;
  width: 20px;
  height: 20px;
  line-height: 20px;
}
[dir="ltr"] .mobile-bar .cart-switcher .cart-count {
  left: 0;
}
[dir="rtl"] .mobile-bar .cart-switcher .cart-count {
  right: 0;
}
.mobile-bar .header-switcher {
  width: 30px;
  height: 30px;
  line-height: 30px;
}
[dir="ltr"] .mobile-bar .header-element:not(:last-child) {
  margin: 0 5px 0 0;
}
[dir="rtl"] .mobile-bar .header-element:not(:last-child) {
  margin: 0 0 0 5px;
}
@keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
@-webkit-keyframes slide-down {
  0% {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
.slider-container {
  overflow: hidden;
}
@media (max-width: 425px) {
  .slider-container {
    height: 50vw !important;
  }
}
@media (min-width: 768px) {
  .slider-container {
    height: 500px;
  }
}
.slider-container .slick-list,
.slider-container .slick-track,
.slider-container .slider {
  height: 100%;
  position: relative;
}
.slider-container .slide-overlay {
  position: relative;
}
.slider-container .slide-overlay:before {
  content: "";
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
[dir] .slider-container .slide-overlay:before {
  background-color: rgba(0, 0, 0, 0.5);
}
[dir="ltr"] .slider-container .slide-overlay:before {
  left: 0;
}
[dir="rtl"] .slider-container .slide-overlay:before {
  right: 0;
}
.slider-container .slide {
  position: relative;
  height: 100%;
  outline: none;
  width: 100%;
}
.slider-container .image-position-center {
  position: absolute !important;
  height: 100%;
}
.slider-container .image-position-top {
  position: absolute !important;
  height: auto;
  top: 0;
}
.slider-container .image-position-bottom {
  position: absolute !important;
  height: auto;
  bottom: 0;
}
.slider-container .desktop-image {
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
}
[dir] .slider-container .desktop-image {
  margin: auto;
}
@media (max-width: 425px) {
  .slider-container .desktop-image {
    display: none;
  }
}
.slider-container .slider-caption {
  position: absolute;
  top: 50%;
  z-index: 3;
  word-break: break-word;
}
[dir] .slider-container .slider-caption {
  text-align: center;
}
[dir="ltr"] .slider-container .slider-caption {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .slider-container .slider-caption {
  right: 50%;
  transform: translate(50%, -50%);
}
@media (max-width: 425px) {
  .slider-container .slider-caption {
    width: 100%;
  }
  [dir] .slider-container .slider-caption {
    padding: 0 15px;
  }
}
.slider-container .slick-arrow {
  position: absolute;
  top: 50%;
  color: #fff;
  z-index: 1;
  font-size: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  transition: all 0.8s ease;
}
.slider-container .slick-arrow:before {
  font-family: FontAwesome;
  font-size: 35px;
}
[dir] .slider-container .slick-arrow.slick-prev {
  transform: translateY(-50%);
}
[dir="ltr"] .slider-container .slick-arrow.slick-prev {
  left: 40px;
}
[dir="rtl"] .slider-container .slick-arrow.slick-prev {
  right: 40px;
}
.slider-container .slick-arrow.slick-prev:before {
  content: "\F104";
}
[dir] .slider-container .slick-arrow.slick-next {
  transform: translateY(-50%);
}
[dir="ltr"] .slider-container .slick-arrow.slick-next {
  right: 40px;
}
[dir="rtl"] .slider-container .slick-arrow.slick-next {
  left: 40px;
}
.slider-container .slick-arrow.slick-next:before {
  content: "\F105";
}
.slider-container .slick-arrow.slick-disabled {
  opacity: 0.8;
}
[dir] .slider-container .slick-arrow.slick-disabled {
  cursor: not-allowed;
}
@media (max-width: 425px) {
  .slider-container .slick-arrow {
    display: none !important;
  }
}
.slider-container .slider-heading {
  display: block;
  font-size: 25px;
  font-weight: 700;
  color: #fff;
}
[dir] .slider-container .slider-heading {
  margin: 0 0 10px;
}
@media (max-width: 425px) {
  .slider-container .slider-heading {
    font-size: 20px;
  }
  [dir] .slider-container .slider-heading {
    margin: 0 0 5px;
  }
}
.slider-container .slider-subheading {
  display: block;
  color: #fff;
  font-size: 15px;
  line-height: 1.4;
}
@media (max-width: 425px) {
  .slider-container .slider-subheading {
    font-size: 14px;
  }
}
@media (min-width: 425px) {
  .slider-container.medium .slider-heading {
    font-size: 35px;
  }
  .slider-container.medium .slider-subheading {
    font-size: 16px;
  }
  .slider-container.large .slider-heading {
    font-size: 45px;
  }
  .slider-container.large .slider-subheading {
    font-size: 18px;
  }
}
.slider-container .slick-dots {
  position: absolute;
  bottom: 20px;
  display: flex;
  align-items: center;
  list-style: none;
}
[dir] .slider-container .slick-dots {
  padding: 0;
  margin: 0;
}
[dir="ltr"] .slider-container .slick-dots {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .slider-container .slick-dots {
  right: 50%;
  transform: translate(50%, -50%);
}
[dir="ltr"] .slider-container .slick-dots li:not(:last-child) {
  margin-right: 10px;
}
[dir="rtl"] .slider-container .slick-dots li:not(:last-child) {
  margin-left: 10px;
}
@media (max-width: 425px) {
  [dir="ltr"] .slider-container .slick-dots li:not(:last-child) {
    margin-right: 8px;
  }
  [dir="rtl"] .slider-container .slick-dots li:not(:last-child) {
    margin-left: 8px;
  }
}
.slider-container .slick-dots li button {
  display: block;
  font-size: 0;
  width: 12px;
  height: 12px;
  line-height: 12px;
  transition: background-color 0.25s;
}
[dir] .slider-container .slick-dots li button {
  background-color: #fff;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.3);
  border: 1px solid #f0f0f0;
  border-radius: 50%;
}
@media (max-width: 425px) {
  .slider-container .slick-dots li button {
    width: 10px;
    height: 10px;
    line-height: 10px;
  }
}
[dir] .slider-container .slick-dots li.slick-active button {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
@media (max-width: 425px) {
  .slider-container .slick-dots {
    bottom: 5px;
  }
}
.slider-container.vertical .slick-vertical .slick-slide {
  height: 100%;
}
[dir] .slider-container.vertical .slick-vertical .slick-slide {
  border-width: 0;
}
@media (max-width: 425px) {
  .slider-container.vertical .slick-vertical .slick-slide {
    height: 50vw;
  }
}
.slider-container.vertical .slick-track {
  height: 100% !important;
}
@media (min-width: 425px) {
  .slider-container.vertical .slick-list {
    height: 100% !important;
  }
}
.slider-container.show-first-image .slide:not(:first-child) .slider-caption,
.slider-container.show-first-image .slide:not(:first-child) img {
  display: none;
}
.slider-container .is-link {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
[dir="ltr"] .slider-container .is-link {
  left: 0;
}
[dir="rtl"] .slider-container .is-link {
  right: 0;
}
[dir] .product-item {
  border: 1px solid #f0f0f0;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.product-item .product-thumbnail {
  display: block;
  position: relative;
  height: 0;
  overflow: hidden;
}
[dir] .product-item .product-thumbnail {
  padding: 0 0 100%;
}
.product-item .product-thumbnail img {
  position: absolute;
  top: 50%;
  max-height: 100%;
  text-indent: -9999px;
  transition: transform 0.25s ease;
}
[dir="ltr"] .product-item .product-thumbnail img {
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
}
[dir="rtl"] .product-item .product-thumbnail img {
  right: 50%;
  transform: translate(50%, -50%) scale(1);
}
.product-item .product-details {
  transition: border 0.25s;
}
[dir] .product-item .product-details {
  padding: 20px 15px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
.product-item .product-title {
  font-size: 14px;
}
[dir] .product-item .product-title {
  margin: 0 0 10px;
}
.product-item .product-title a {
  display: block;
  color: inherit;
}
.product-item .product-price {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.product-item .product-price .before {
  color: #747474;
  text-decoration: line-through;
}
[dir] .product-item .product-price .before {
  margin: 0 0 5px;
}
.product-item .product-price .after {
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-color);
}
[dir] .product-item .product-actions {
  margin: 15px 0 0;
}
.product-item .product-actions .button {
  min-width: 190px;
  font-weight: 600;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[dir="ltr"] .product-item:hover .product-thumbnail img {
  transform: translate(-50%, -50%) scale(1.1);
}
[dir="rtl"] .product-item:hover .product-thumbnail img {
  transform: translate(50%, -50%) scale(1.1);
}
[dir] .product-item:hover,
[dir] .product-item:hover .product-details {
  border-color: var(--primary-color);
}
.products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 25px;
}
@media (max-width: 425px) {
  .products-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (min-width: 425px) and (max-width: 1124px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 425px) and (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.products-style-1 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 25px 20px;
}
[dir] .products-style-1 .product-item {
  box-shadow: none;
  border: 0;
}
[dir] .products-style-1 .product-item .product-details {
  padding-bottom: 0;
  border-top: 0;
}
.products-style-1 .product-item .product-actions {
  display: none;
}
@media (max-width: 1124px) {
  .products-style-1 {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 425px) {
  .products-style-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}
.products-style-2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 25px;
}
@media (max-width: 425px) {
  .products-style-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.products-style-2 .product-actions {
  display: none;
}
.products-style-2 .product-item {
  position: relative;
}
.products-style-2 .product-item .product-details {
  position: absolute;
  bottom: 0;
  width: 100%;
}
[dir] .products-style-2 .product-item .product-details {
  background-color: hsla(0, 0%, 100%, 0.8);
  padding: 15px;
}
[dir="ltr"] .products-style-2 .product-item .product-details {
  text-align: left;
}
[dir="rtl"] .products-style-2 .product-item .product-details {
  text-align: right;
}
[dir] .products-style-2 .product-item .product-title {
  margin: 0 0 5px;
}
.products-style-2 .product-item .product-price {
  flex-direction: row;
  justify-content: space-between;
}
[dir] .products-style-2 .product-item .product-price .before {
  margin: 0;
}
[dir] .products-slider .product-item {
  margin: 0 12.5px;
}
.products-slider .slick-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  font-size: 0;
  color: #fff;
  z-index: 1;
}
[dir] .products-slider .slick-arrow {
  background-color: var(--primary-color);
  text-align: center;
  border-radius: 3px;
  border: 1px solid var(--primary-color);
  transform: translateY(-50%);
}
.products-slider .slick-arrow:before {
  font-family: icons;
  font-size: 16px;
  font-weight: 600;
}
[dir="ltr"] .products-slider .slick-prev {
  left: 0;
  padding: 0 1px 0 0;
}
[dir="rtl"] .products-slider .slick-prev {
  right: 0;
  padding: 0 0 0 1px;
}
.products-slider .slick-prev:before {
  content: "\E904";
}
[dir="ltr"] .products-slider .slick-next {
  right: 0;
  padding: 0 0 0 1px;
}
[dir="rtl"] .products-slider .slick-next {
  left: 0;
  padding: 0 1px 0 0;
}
.products-slider .slick-next:before {
  content: "\E905";
}
.single-product {
  width: 100%;
}
@media (min-width: 768px) {
  .single-product .product-wrapper {
    display: flex;
    align-items: flex-start;
  }
}
@media (min-width: 425px) and (max-width: 768px) {
  .single-product .product-wrapper {
    flex-direction: column;
  }
  [dir] .single-product .product-wrapper .product-preview {
    margin: 0 auto 30px;
  }
}
.single-product .product-wrapper .product-preview {
  width: 500px;
  display: none;
}
@media (min-width: 768px) {
  .single-product .product-wrapper .product-preview {
    position: sticky;
    display: block;
  }
}
@media (min-width: 1124px) {
  .single-product .product-wrapper .product-preview {
    top: 130px;
  }
}
@media (min-width: 768px) and (max-width: 1124px) {
  .single-product .product-wrapper .product-preview {
    top: 90px;
  }
}
[dir]
  .single-product
  .product-wrapper
  .product-details
  .product-section:not(:last-child) {
  margin: 0 0 15px;
}
@media (min-width: 768px) {
  .single-product
    .product-wrapper
    .product-details
    .product-section.preview-wrapper {
    display: none;
  }
}
@media (min-width: 768px) {
  .single-product .product-wrapper .product-details {
    width: calc(100% - 500px);
  }
  [dir="ltr"] .single-product .product-wrapper .product-details {
    padding: 0 0 0 30px;
  }
  [dir="rtl"] .single-product .product-wrapper .product-details {
    padding: 0 30px 0 0;
  }
}
@media (max-width: 768px) {
  .single-product .product-wrapper .product-details {
    max-width: 500px;
  }
  [dir] .single-product .product-wrapper .product-details {
    margin: auto;
  }
}
[dir] .single-product .product-wrapper.hide-preview .product-details {
  margin: auto;
  padding: 0;
}
[dir] .single-product .product-wrapper .checkout-section {
  padding: 0;
}
.single-product .product-wrapper .checkout-section .main {
  width: 100%;
}
[dir] .single-product .product-wrapper .checkout-section .main {
  padding: 0;
}
.single-product .preview {
  height: 0;
  overflow: hidden;
}
[dir] .single-product .preview {
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  padding: 0 0 calc(100% - 2px);
}
[dir] .single-product .preview img {
  margin: auto;
}
.single-product .preview .preview-item {
  outline: none;
}
.single-product .preview iframe {
  position: relative;
  width: 100%;
  z-index: 2;
}
[dir] .single-product .thumbnails {
  margin: 10px 0 0;
}
.single-product .thumbnails .thumbnail-item {
  outline: none;
}
[dir] .single-product .thumbnails .thumbnail-item {
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  margin: 0 5px;
  cursor: pointer;
}
.single-product .thumbnails .thumbnail-item.slick-current {
  position: relative;
}
.single-product .thumbnails .thumbnail-item.slick-current:before {
  content: "";
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
[dir] .single-product .thumbnails .thumbnail-item.slick-current:before {
  background-color: hsla(0, 0%, 100%, 0.6);
}
[dir="ltr"] .single-product .thumbnails .thumbnail-item.slick-current:before {
  left: 0;
}
[dir="rtl"] .single-product .thumbnails .thumbnail-item.slick-current:before {
  right: 0;
}
.single-product .thumbnails .thumbnail-item.is-video-item {
  position: relative;
}
.single-product .thumbnails .thumbnail-item.is-video-item:after {
  content: "\F01D";
  font-family: FontAwesome;
  position: absolute;
  top: 50%;
  width: 40px;
  height: 40px;
  line-height: 42px;
  font-size: 20px;
  color: var(--danger-color);
}
[dir] .single-product .thumbnails .thumbnail-item.is-video-item:after {
  background-color: hsla(0, 0%, 100%, 0.9);
  text-align: center;
  border-radius: 50%;
}
[dir="ltr"] .single-product .thumbnails .thumbnail-item.is-video-item:after {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .single-product .thumbnails .thumbnail-item.is-video-item:after {
  right: 50%;
  transform: translate(50%, -50%);
}
.single-product .thumbnails .thumbnail-preview {
  height: 0;
  overflow: hidden;
}
[dir] .single-product .thumbnails .thumbnail-preview {
  padding: 0 0 100%;
}
.single-product .thumbnails .slick-arrow {
  position: absolute;
  top: 50%;
  width: 36px;
  height: 36px;
  line-height: 36px;
  font-size: 0;
  z-index: 1;
}
[dir] .single-product .thumbnails .slick-arrow {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  text-align: center;
  transform: translateY(-50%);
  border-radius: 3px;
}
.single-product .thumbnails .slick-arrow:before {
  font-family: icons;
  font-size: 16px;
}
[dir="ltr"] .single-product .thumbnails .slick-prev {
  left: 0;
  padding: 0 1px 0 0;
}
[dir="rtl"] .single-product .thumbnails .slick-prev {
  right: 0;
  padding: 0 0 0 1px;
}
.single-product .thumbnails .slick-prev:before {
  content: "\E904";
}
[dir="ltr"] .single-product .thumbnails .slick-next {
  right: 0;
  padding: 0 0 0 1px;
}
[dir="rtl"] .single-product .thumbnails .slick-next {
  left: 0;
  padding: 0 1px 0 0;
}
.single-product .thumbnails .slick-next:before {
  content: "\E905";
}
[dir] .single-product .thumbnails .slick-disabled {
  cursor: not-allowed;
  background-color: #fafafa;
}
.single-product .section-reviews .modal {
  position: fixed;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 3;
  color: #1a1a1a;
}
[dir] .single-product .section-reviews .modal {
  background-color: rgba(0, 0, 0, 0.8);
}
[dir="ltr"] .single-product .section-reviews .modal {
  left: 0;
}
[dir="rtl"] .single-product .section-reviews .modal {
  right: 0;
}
.single-product .section-reviews .modal .modal-content {
  min-width: 500px;
  max-width: 600px;
}
[dir] .single-product .section-reviews .modal .modal-content {
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 0 20px -10px hsla(0, 0%, 100%, 0.6);
}
@media (max-width: 768px) {
  .single-product .section-reviews .modal .modal-content {
    min-width: 0;
    width: 94%;
  }
}
.single-product .section-reviews .modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .single-product .section-reviews .modal .modal-header {
  border-bottom: 1px solid #f0f0f0;
}
.single-product .section-reviews .modal .modal-header h4 {
  font-size: 15px;
  font-weight: 600;
}
.single-product .section-reviews .modal .modal-header .close {
  font-size: 18px;
  color: inherit;
}
.single-product .section-reviews .modal .modal-body {
  max-height: 450px;
  overflow-y: auto;
}
.single-product .section-reviews .modal .modal-body form {
  display: grid;
}
.single-product .section-reviews .modal .modal-body form,
.single-product .section-reviews .modal .modal-body form .is-grid {
  grid-gap: 15px;
}
.single-product .section-reviews .modal .modal-body .form-label {
  display: block;
  line-height: 1;
}
[dir] .single-product .section-reviews .modal .modal-body .form-label {
  margin-bottom: 10px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container {
  width: 100%;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container {
  border: 2px dashed #f0f0f0;
  border-radius: 3px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image {
  height: 100%;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image
  .image-overlay {
  position: absolute;
  top: 50%;
  height: 55%;
  width: 90%;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image
  .image-overlay {
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.5);
}
[dir="ltr"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image
  .image-overlay {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image
  .image-overlay {
  right: 50%;
  transform: translate(50%, -50%);
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-container
  .preview-image
  .icon-overlay {
  width: 30px;
  height: 30px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-list-container {
  min-height: 0;
}
[dir="ltr"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-list-container
  .image-list-item:not(:last-child) {
  margin: 0 5px 0 0;
}
[dir="rtl"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .image-list-container
  .image-list-item:not(:last-child) {
  margin: 0 0 0 5px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .thumbnails-uploader
  .browse-text {
  color: var(--primary-color);
}
[dir] .single-product .section-reviews .modal .modal-body .success-review {
  text-align: center;
}
.single-product .section-reviews .modal .modal-body .success-review h2 {
  font-size: 22px;
}
[dir] .single-product .section-reviews .modal .modal-body .success-review p {
  margin: 5px 0 20px;
}
.single-product .section-reviews .modal .modal-body .modal-thumbnails {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
}
[dir] .single-product .section-reviews .modal .modal-body .modal-thumbnails {
  margin: 0 0 10px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .modal-thumbnails
  .modal-thumbnail {
  position: relative;
  height: 0;
  overflow: hidden;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .modal-thumbnails
  .modal-thumbnail {
  padding: 0 0 100%;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .modal-thumbnails
  .modal-thumbnail
  img {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}
[dir="ltr"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .modal-thumbnails
  .modal-thumbnail
  img {
  left: 0;
}
[dir="rtl"]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .modal-thumbnails
  .modal-thumbnail
  img {
  right: 0;
}
[dir] .single-product .section-reviews .modal .modal-body .review-info {
  background-color: #fafafa;
  padding: 10px;
  border-radius: 3px;
  border: 1px solid #f0f0f0;
  text-align: center;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .review-info
  .reviewer {
  margin: 0 0 10px;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .review-info
  .vue-star-rating {
  margin: 0 auto;
}
.single-product
  .section-reviews
  .modal
  .modal-body
  .review-info
  .review-content {
  word-break: break-word;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body
  .review-info
  .review-content {
  margin: 5px 0 0;
}
.single-product .section-reviews .modal .modal-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
}
[dir]
  .single-product
  .section-reviews
  .modal
  .modal-body::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}
@media (max-width: 425px) {
  .single-product .section-reviews .modal .modal-body {
    max-height: 350px;
  }
  .single-product .section-reviews .modal .modal-body .grid-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.single-product .section-reviews .modal .modal-footer {
  display: flex;
  justify-content: flex-end;
}
[dir] .single-product .section-reviews .modal .modal-footer {
  border-top: 1px solid #f0f0f0;
}
[dir] .single-product .section-reviews .modal .modal-body,
[dir] .single-product .section-reviews .modal .modal-footer,
[dir] .single-product .section-reviews .modal .modal-header {
  padding: 15px;
}
@media (max-width: 425px) {
  .single-product .section-reviews .share-box {
    flex-direction: column;
  }
}
@media (max-width: 425px) {
  [dir] .single-product .section-reviews .share-box .share:not(:last-child) {
    margin: 0 0 10px;
  }
}
[dir] .single-product .section-reviews .share-box .share a {
  border-radius: 2px;
}
.single-product .section-related.hide,
.single-product .section-reviews .share-box .share a .fa,
.single-product .section-reviews .share-box .share a .yc {
  display: none;
}
[dir] .single-product .section-related,
[dir] .single-product .section-reviews {
  padding: 60px 0 0;
}
@media (max-width: 1124px) {
  [dir] .single-product .section-related,
  [dir] .single-product .section-reviews {
    padding: 30px 0 0;
  }
}
@media (min-width: 768px) {
  .single-product .fixed-share-box {
    position: fixed;
    top: 300px;
    z-index: 1;
  }
  [dir="ltr"] .single-product .fixed-share-box {
    left: 40px;
  }
  [dir="rtl"] .single-product .fixed-share-box {
    right: 40px;
  }
  .single-product .fixed-share-box .share-box {
    flex-direction: column;
  }
  [dir] .single-product .fixed-share-box .share:not(:last-child) {
    margin: 0 0 10px;
  }
  .single-product .fixed-share-box .share a {
    width: 45px;
    height: 45px;
  }
  [dir] .single-product .fixed-share-box .share a {
    border-radius: 50%;
  }
  .single-product .fixed-share-box .share a .yc {
    display: block;
  }
  [dir] .single-product .fixed-share-box .share a .yc {
    margin: 0;
  }
  .single-product .fixed-share-box .share a span {
    font-size: 0;
  }
}
@media (max-width: 425px) {
  [dir] .single-product .fixed-share-box .share:not(:last-child) {
    margin: 0;
  }
}
@media (max-width: 768px) {
  [dir] .single-product .fixed-share-box {
    margin: 15px 0 0;
  }
}
.single-product .sticky-cart-bar {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 1;
}
[dir] .single-product .sticky-cart-bar {
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 10px 0;
  box-shadow: 0 -10px 20px -20px rgba(0, 0, 0, 0.1);
}
[dir="ltr"] .single-product .sticky-cart-bar {
  left: 0;
}
[dir="rtl"] .single-product .sticky-cart-bar {
  right: 0;
}
.single-product .sticky-cart-bar .container {
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-product .sticky-cart-bar .add-to-cart-section {
  width: 435px;
}
@media (max-width: 768px) {
  .single-product .sticky-cart-bar .add-to-cart-section {
    width: 100%;
  }
}
[dir="ltr"] .single-product .sticky-cart-bar .share-box {
  margin: 0 0 0 15px;
}
[dir="rtl"] .single-product .sticky-cart-bar .share-box {
  margin: 0 15px 0 0;
}
.single-product .sticky-cart-bar .share-box .share a {
  height: 45px;
}
[dir] .single-product .sticky-cart-bar .share-box .share a {
  border-radius: 3px;
}
@media (max-width: 768px) {
  .single-product .sticky-cart-bar .share-box,
  .single-product .sticky-cart-bar.show-on-desktop {
    display: none;
  }
}
@media (min-width: 768px) {
  .single-product .sticky-cart-bar.show-on-mobile {
    display: none;
  }
}
.single-product .sticky-cart-bar.show-on-desktop.show-on-mobile {
  display: block;
}
@media (max-width: 425px) {
  [dir] .single-product {
    padding-top: 15px;
  }
}
.reviews-body .add-reviews {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .reviews-body .add-reviews {
  margin: 0 0 30px;
}
@media (max-width: 425px) {
  .reviews-body .add-reviews {
    flex-direction: column;
  }
  [dir] .reviews-body .add-reviews h3 {
    margin: 0 0 10px;
  }
}
.reviews-body .reviews-grid {
  -moz-column-count: 4;
  column-count: 4;
  -moz-column-gap: 15px;
  column-gap: 15px;
}
@media (max-width: 768px) {
  .reviews-body .reviews-grid {
    -moz-column-count: 3;
    column-count: 3;
  }
}
@media (max-width: 480px) {
  .reviews-body .reviews-grid {
    -moz-column-count: 2;
    column-count: 2;
  }
}
@media (max-width: 425px) {
  .reviews-body .reviews-grid {
    -moz-column-count: 1;
    column-count: 1;
  }
  [dir] .reviews-body .reviews-grid {
    margin: 0 0 -15px;
  }
}
.reviews-body .review-card {
  position: relative;
}
[dir] .reviews-body .review-card {
  margin: 0 0 15px;
}
.reviews-body .review {
  display: grid;
  width: 100%;
  -moz-column-break-inside: avoid;
  break-inside: avoid-column;
  page-break-inside: avoid;
}
[dir] .reviews-body .review {
  border: 1px solid #f0f0f0;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.reviews-body .review-thumbnail {
  height: 100%;
  width: 100%;
  min-height: 275px;
}
@media (max-width: 425px) {
  .reviews-body .review-thumbnail {
    min-height: 243px;
  }
}
.reviews-body .review-thumbnail img {
  display: inherit;
}
.reviews-body .review-thumbnail .thumbnail {
  height: 100%;
  width: 100%;
  -o-object-fit: initial;
  object-fit: fill;
}
[dir] .reviews-body .review-thumbnail .thumbnail {
  border-radius: 3px 3px 0 0;
}
.reviews-body .review-thumbnail .yc-eye {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #b0b0b0;
  opacity: 0;
  transition: opacity 0.25s;
}
[dir] .reviews-body .review-thumbnail .yc-eye {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}
[dir="ltr"] .reviews-body .review-thumbnail .yc-eye {
  left: 0;
}
[dir="rtl"] .reviews-body .review-thumbnail .yc-eye {
  right: 0;
}
.reviews-body .review-thumbnail .yc-eye:before {
  width: 50px;
  height: 50px;
  line-height: 50px;
}
[dir] .reviews-body .review-thumbnail .yc-eye:before {
  text-align: center;
  border-radius: 50%;
  border: 1px solid #f0f0f0;
}
.reviews-body .review-thumbnail:hover .yc-eye {
  opacity: 1;
}
[dir] .reviews-body .review-details {
  text-align: center;
  padding: 15px;
  border-top: 1px solid #f0f0f0;
}
[dir] .reviews-body .name-rating,
[dir] .reviews-body .reviewer {
  margin: 0 0 8px;
}
.reviews-body .vue-star-rating {
  justify-content: center;
}
.reviews-body .review-description p {
  font-size: 13px;
  word-break: break-word;
}
[dir] .reviews-body .review-description p {
  margin: 0;
}
.reviews-body .more-reviews {
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .reviews-body .more-reviews {
  margin: 20px 0 0;
}
.single-title {
  line-height: 1.3;
  font-size: 25px;
  font-weight: 600;
}
.single-price {
  display: flex;
  align-items: center;
}
@media (max-width: 425px) {
  .single-price {
    display: block;
  }
  .single-price:after {
    content: "";
    display: table;
  }
  [dir] .single-price:after {
    clear: both;
  }
}
.single-price .after {
  color: var(--primary-color);
  font-size: 26px;
  font-weight: 600;
  line-height: 24px;
}
.single-price .before {
  font-size: 18px;
  text-decoration: line-through;
  font-weight: 400;
  color: #747474;
}
[dir="ltr"] .single-price .before {
  margin: 2px 0 0 15px;
}
[dir="rtl"] .single-price .before {
  margin: 2px 15px 0 0;
}
@media (max-width: 425px) {
  [dir] .single-price .before {
    margin: 2px 0 0;
  }
  [dir="ltr"] .single-price .before {
    float: right;
  }
  [dir="rtl"] .single-price .before {
    float: left;
  }
}
[dir] .single-variants .single-variant:not(:last-child) {
  margin-bottom: 20px;
}
.single-variants .single-variant .option-name {
  font-size: 15px;
  font-weight: 600;
  display: block;
}
[dir] .single-variants .single-variant .option-name {
  margin: 0 0 10px;
}
.single-variants .single-variant .color-based-buttons-container,
.single-variants .single-variant .image-based-buttons-container,
.single-variants .single-variant .textual-buttons-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
[dir] .single-variants .single-variant .textual-buttons-container {
  margin: 0 0 -6px;
}
[dir] .textual-button {
  margin: 0 0 6px;
}
[dir="ltr"] .textual-button:not(:last-child) {
  margin: 0 6px 6px 0;
}
[dir="rtl"] .textual-button:not(:last-child) {
  margin: 0 0 6px 6px;
}
.textual-button label {
  display: block;
  position: relative;
  height: 34px;
}
[dir] .textual-button label {
  padding: 5px 12px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  cursor: pointer;
}
.textual-button [type="radio"] {
  position: absolute;
}
[dir="ltr"] .textual-button [type="radio"] {
  left: -9999px;
}
[dir="rtl"] .textual-button [type="radio"] {
  right: -9999px;
}
.textual-button [type="radio"]:checked + label {
  font-weight: 600;
  color: #fff;
}
.radio-buttons-container label {
  color: inherit;
}
[dir] .radio-buttons-container .radio-button-variant:not(:last-child) {
  margin: 0 0 12px;
}
.radio-buttons-container [type="radio"]:checked,
.radio-buttons-container [type="radio"]:not(:checked) {
  position: absolute;
}
[dir="ltr"] .radio-buttons-container [type="radio"]:checked,
[dir="ltr"] .radio-buttons-container [type="radio"]:not(:checked) {
  left: -9999px;
}
[dir="rtl"] .radio-buttons-container [type="radio"]:checked,
[dir="rtl"] .radio-buttons-container [type="radio"]:not(:checked) {
  right: -9999px;
}
.radio-buttons-container [type="radio"]:checked + label,
.radio-buttons-container [type="radio"]:not(:checked) + label {
  position: relative;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}
[dir] .radio-buttons-container [type="radio"]:checked + label,
[dir] .radio-buttons-container [type="radio"]:not(:checked) + label {
  cursor: pointer;
}
[dir="ltr"] .radio-buttons-container [type="radio"]:checked + label,
[dir="ltr"] .radio-buttons-container [type="radio"]:not(:checked) + label {
  padding: 1px 0 0 32px;
}
[dir="rtl"] .radio-buttons-container [type="radio"]:checked + label,
[dir="rtl"] .radio-buttons-container [type="radio"]:not(:checked) + label {
  padding: 1px 32px 0 0;
}
.radio-buttons-container [type="radio"]:checked + label:before,
.radio-buttons-container [type="radio"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  top: 0;
  width: 23px;
  height: 23px;
}
[dir] .radio-buttons-container [type="radio"]:checked + label:before,
[dir] .radio-buttons-container [type="radio"]:not(:checked) + label:before {
  border: 1px solid #e5e5e5;
  border-radius: 100%;
  background-color: #fff;
}
[dir="ltr"] .radio-buttons-container [type="radio"]:checked + label:before,
[dir="ltr"]
  .radio-buttons-container
  [type="radio"]:not(:checked)
  + label:before {
  left: 0;
}
[dir="rtl"] .radio-buttons-container [type="radio"]:checked + label:before,
[dir="rtl"]
  .radio-buttons-container
  [type="radio"]:not(:checked)
  + label:before {
  right: 0;
}
.radio-buttons-container [type="radio"]:checked + label:after,
.radio-buttons-container [type="radio"]:not(:checked) + label:after {
  content: "";
  width: 13px;
  height: 13px;
  position: absolute;
  top: 6px;
  transition: all 0.2s ease;
}
[dir] .radio-buttons-container [type="radio"]:checked + label:after,
[dir] .radio-buttons-container [type="radio"]:not(:checked) + label:after {
  background-color: var(--primary-color);
  border-radius: 100%;
}
[dir="ltr"] .radio-buttons-container [type="radio"]:checked + label:after,
[dir="ltr"]
  .radio-buttons-container
  [type="radio"]:not(:checked)
  + label:after {
  left: 6px;
}
[dir="rtl"] .radio-buttons-container [type="radio"]:checked + label:after,
[dir="rtl"]
  .radio-buttons-container
  [type="radio"]:not(:checked)
  + label:after {
  right: 6px;
}
.radio-buttons-container [type="radio"]:not(:checked) + label:after {
  opacity: 0;
}
[dir] .radio-buttons-container [type="radio"]:not(:checked) + label:after {
  transform: scale(0);
}
.radio-buttons-container [type="radio"]:checked + label:after {
  opacity: 1;
}
[dir] .radio-buttons-container [type="radio"]:checked + label:after {
  transform: scale(1);
}
[dir] .color-based-buttons-container,
[dir] .image-based-buttons-container {
  margin: 0 0 -10px;
}
[dir="ltr"] .color-based-buttons-container,
[dir="ltr"] .image-based-buttons-container {
  padding: 0 0 0 4px;
}
[dir="rtl"] .color-based-buttons-container,
[dir="rtl"] .image-based-buttons-container {
  padding: 0 4px 0 0;
}
.color-based-buttons,
.image-based-buttons {
  position: relative;
  width: 40px;
  height: 35px;
}
[dir] .color-based-buttons,
[dir] .image-based-buttons {
  border: 2px solid #fff;
  margin: 0 0 10px;
}
[dir="ltr"] .color-based-buttons:not(:last-child),
[dir="ltr"] .image-based-buttons:not(:last-child) {
  margin: 0 10px 10px 0;
}
[dir="rtl"] .color-based-buttons:not(:last-child),
[dir="rtl"] .image-based-buttons:not(:last-child) {
  margin: 0 0 10px 10px;
}
.image-based-buttons {
  width: 100px;
  height: 100px;
}
[dir] .image-based-buttons {
  background-size: contain;
}
.image-based-buttons .thumbnail {
  height: 0;
  overflow: hidden;
}
[dir] .image-based-buttons .thumbnail {
  padding: 0 0 100%;
}
.color-based-buttons:after,
.image-based-buttons:after {
  content: "";
  position: absolute;
  top: -4px;
  bottom: -4px;
}
[dir] .color-based-buttons:after,
[dir] .image-based-buttons:after {
  border: 2px solid #e5e5e5;
  border-radius: 3px;
}
[dir="ltr"] .color-based-buttons:after,
[dir="ltr"] .image-based-buttons:after,
[dir="rtl"] .color-based-buttons:after,
[dir="rtl"] .image-based-buttons:after {
  left: -4px;
  right: -4px;
}
[dir] .color-based-buttons.selected:after,
[dir] .image-based-buttons.selected:after {
  border-color: #1a1a1a;
}
.upload-image-zone {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .upload-image-zone {
  border: 2px dashed #e5e5e5;
  border-radius: 3px;
}
@media (max-width: 425px) {
  .upload-image-zone {
    width: 100%;
  }
}
.upload-image-zone form {
  width: 100%;
  height: 100%;
}
[dir="ltr"] .upload-image-zone form {
  left: 0;
}
[dir="rtl"] .upload-image-zone form {
  right: 0;
}
.upload-image-zone .upload-image-label {
  display: block;
  height: 100%;
}
.upload-image-zone .upload-image-label img {
  max-height: 220px;
}
[dir] .upload-image-zone .upload-image-label img {
  margin: auto;
}
.upload-image-zone span {
  font-weight: 600;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: inherit;
  width: 100%;
  height: 100%;
}
[dir] .upload-image-zone span {
  text-align: center;
  padding: 20px 10px;
}
.upload-image-zone span i {
  font-size: 34px;
  height: 34px;
}
[dir] .upload-image-zone span i {
  margin-bottom: 10px;
}
.upload-image-zone input[type="file"] {
  display: none;
}
.dropdown-container {
  position: relative;
}
.dropdown-container .dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 45px;
}
[dir] .dropdown-container .dropdown-header {
  background-color: #fafafa;
  padding: 0 15px;
  cursor: pointer;
  border-radius: 3px;
  border: 1px solid #f0f0f0;
}
.dropdown-container .dropdown-header .selected-text {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}
.dropdown-container .dropdown-header .yc {
  font-size: 16px;
  font-weight: 600;
}
.dropdown-container .dropdown-header.selected .fa:before {
  content: "\E906";
}
.dropdown-container .dropdown-menu {
  max-height: 190px;
  overflow-y: scroll;
  position: absolute;
  width: 100%;
  z-index: 1;
}
[dir] .dropdown-container .dropdown-menu {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  margin: 2px 0 0;
  background-color: #fff;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
}
[dir="ltr"] .dropdown-container .dropdown-menu {
  left: 0;
}
[dir="rtl"] .dropdown-container .dropdown-menu {
  right: 0;
}
.dropdown-container .dropdown-menu li {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  position: relative;
  min-height: 45px;
  color: #1a1a1a;
}
[dir] .dropdown-container .dropdown-menu li {
  cursor: pointer;
  padding: 0 15px;
}
[dir] .dropdown-container .dropdown-menu li:not(:last-child) {
  border-bottom: 1px solid #e5e5e5;
}
.dropdown-container .variant-clicked {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}
[dir="ltr"] .dropdown-container .variant-clicked {
  left: 0;
}
[dir="rtl"] .dropdown-container .variant-clicked {
  right: 0;
}
.add-to-cart-section {
  display: flex;
  align-items: center;
}
.add-to-cart-section .quantity {
  position: relative;
  width: 120px;
}
[dir="ltr"] .add-to-cart-section .quantity {
  margin: 0 15px 0 0;
}
[dir="rtl"] .add-to-cart-section .quantity {
  margin: 0 0 0 15px;
}
@media (max-width: 425px) {
  .add-to-cart-section .quantity {
    width: 110px;
  }
}
.add-to-cart-section .quantity .quantity-handler {
  width: 30px;
  height: 30px;
  font-size: 16px;
  font-weight: 600;
  line-height: 28px;
  position: absolute;
  top: 50%;
}
[dir] .add-to-cart-section .quantity .quantity-handler {
  padding: 1px 0 0;
  text-align: center;
  cursor: pointer;
  transform: translateY(-50%);
}
[dir="ltr"] .add-to-cart-section .quantity .quantity-handler-left {
  left: 0;
}
[dir="ltr"] .add-to-cart-section .quantity .quantity-handler-right,
[dir="rtl"] .add-to-cart-section .quantity .quantity-handler-left {
  right: 0;
}
[dir="rtl"] .add-to-cart-section .quantity .quantity-handler-right {
  left: 0;
}
.add-to-cart-section .quantity .single-quantity {
  width: 100%;
  height: 45px;
  font-size: 18px;
  font-weight: 600;
}
[dir] .add-to-cart-section .quantity .single-quantity {
  padding: 0;
  text-align: center;
  border: 0;
  box-shadow: none;
}
[dir] .add-to-cart-section .quantity-handler {
  background-color: #f0f0f0;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.add-to-cart-section .single-submit {
  width: calc(100% - 135px);
  height: 45px;
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  flex: auto;
}
.add-to-cart-section .single-submit:disabled {
  opacity: 0.6;
  font-size: 14px;
}
@media (max-width: 425px) {
  .add-to-cart-section .single-submit {
    width: calc(100% - 110px);
  }
}
.single-hurry {
  font-size: 14px;
  letter-spacing: 2.5px;
  font-weight: 700;
}
[dir] .single-hurry {
  text-align: center;
  margin: 5px 0;
}
.single-visitors p {
  font-size: 14px;
  font-weight: 700;
  line-height: 23px;
}
[dir] .single-visitors p {
  text-align: center;
  margin: 0;
}
.single-visitors b {
  display: inline-block;
  color: #fff;
  font-weight: 700;
}
[dir] .single-visitors b {
  background-color: var(--primary-color);
  border-radius: 3px;
  padding: 2px 12px;
  margin: 0 5px;
}
.single-progress {
  overflow: hidden;
}
[dir] .single-progress {
  margin: 5px 8px 20px;
  border-radius: 10px;
  box-shadow: 0 0 0 7px #f0f0f0, inset 0 0 0 2px #f0f0f0;
}
.single-progress span {
  display: block;
  height: 7px;
}
[dir] .single-progress span {
  border-radius: 10px;
  background-color: var(--primary-color);
}
[dir="ltr"] .single-progress span {
  background-image: repeating-linear-gradient(
    -45deg,
    var(--primary-color),
    var(--primary-color) 7px,
    var(--dark-primary-color) 0,
    var(--dark-primary-color) 8px
  );
}
[dir="rtl"] .single-progress span {
  background-image: repeating-linear-gradient(
    45deg,
    var(--primary-color),
    var(--primary-color) 7px,
    var(--dark-primary-color) 0,
    var(--dark-primary-color) 8px
  );
}
.single-countdown {
  display: flex;
  align-items: flex-start;
}
.single-countdown .duration {
  flex: 1 0 auto;
  font-size: 34px;
  line-height: 1.2;
  text-transform: capitalize;
  font-weight: 700;
  position: relative;
}
[dir] .single-countdown .duration {
  text-align: center;
}
.single-countdown .duration:not(:last-child):after {
  content: ":";
  position: absolute;
  top: 0;
}
[dir="ltr"] .single-countdown .duration:not(:last-child):after {
  right: 0;
}
[dir="rtl"] .single-countdown .duration:not(:last-child):after {
  left: 0;
}
.single-countdown .duration span {
  display: block;
}
.single-countdown .duration span:last-child {
  font-weight: 400;
  font-size: 12px;
  text-transform: capitalize;
  line-height: 1.2;
}
.description-section {
  overflow: hidden;
}
[dir] .description-section {
  padding: 15px 0 0;
  border-top: 1px solid #f0f0f0;
}
.description-section .more {
  display: block;
  font-size: 13px;
}
[dir] .description-section .more {
  margin: 10px 0 0;
}
[dir="ltr"] .description-section .more {
  float: right;
}
[dir="rtl"] .description-section .more {
  float: left;
}
.single-description {
  overflow: hidden;
  word-break: break-word;
}
[dir] .single-description p {
  margin: 0;
}
[dir] .single-description p:not(:last-child) {
  margin: 0 0 10px;
}
.single-description.truncate {
  height: 100px;
}
.single-description.show {
  height: auto;
}
.single-description * {
  font-family: inherit !important;
}
[dir] .is-sticky {
  padding: 0 0 66px;
}
@media (max-width: 768px) {
  [dir] .is-sticky-mobile {
    padding: 0 0 66px;
  }
}
@media (min-width: 768px) {
  [dir] .is-sticky-desktop {
    padding: 0 0 66px;
  }
}
[dir] #single-product .single-product {
  padding: 60px 0;
}
@media (min-width: 425px) and (max-width: 1124px) {
  [dir] #single-product .single-product {
    padding: 30px 0;
  }
}
@media (max-width: 425px) {
  [dir] #single-product .single-product {
    padding: 15px 0 30px;
  }
}
.section-search .search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .section-search .search-header {
  margin: 0 0 34px;
}
@media (max-width: 425px) {
  .section-search .search-header {
    flex-direction: column;
    justify-content: center;
  }
}
.section-search .search-header .search-heading {
  display: flex;
  align-items: flex-end;
}
@media (max-width: 425px) {
  .section-search .search-header .search-heading {
    flex-direction: column;
    align-items: center;
  }
  [dir] .section-search .search-header .search-heading {
    margin: 0 0 15px;
  }
}
.section-search .search-header h1 {
  font-size: 26px;
  line-height: 1;
}
[dir="ltr"] .section-search .search-header h1 {
  margin: 0 10px 0 0;
}
[dir="rtl"] .section-search .search-header h1 {
  margin: 0 0 0 10px;
}
@media (max-width: 768px) {
  .section-search .search-header h1 {
    font-size: 22px;
  }
}
@media (max-width: 425px) {
  [dir] .section-search .search-header h1 {
    margin: 0 0 10px;
  }
}
.section-search .search-header p {
  color: #696969;
}
[dir] .section-search .search-header p {
  margin: 0;
}
.section-search .search-body .more-products {
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .section-search .search-body .empty {
  text-align: center;
}
.section-search .search-body .empty h3 {
  font-size: 32px;
  font-weight: 600;
}
@media (max-width: 425px) {
  .section-search .search-body .empty h3 {
    font-size: 24px;
  }
}
[dir] .section-search .search-body .empty img {
  margin: 24px auto 0;
}
@media (min-width: 768px) {
  .section-search .search-body .empty img {
    max-width: 650px;
  }
}
.product-image-section .vertical-slider > .slick-slider {
  width: 100px;
  position: absolute;
  top: 0;
}
[dir] .product-image-section .vertical-slider > .slick-slider {
  margin: 0 !important;
}
[dir="ltr"] .product-image-section .vertical-slider > .slick-slider {
  left: 0;
}
[dir="rtl"] .product-image-section .vertical-slider > .slick-slider {
  right: 0;
}
.product-image-section .vertical-slider > .slick-slider .slick-track {
  display: grid;
  height: auto !important;
  gap: 5px;
}
.product-image-section .vertical-slider > .slick-slider .slick-track:after,
.product-image-section .vertical-slider > .slick-slider .slick-track:before {
  content: none;
}
.product-image-section .vertical-slider > .slick-slider .slick-slide {
  min-width: 90px;
}
[dir]
  .product-image-section
  .vertical-slider
  > .slick-slider
  .thumbnails
  .thumbnail-preview {
  background: #fff;
}
.product-image-section .vertical-slider > .slick-slider .slick-arrow {
  top: unset !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir="ltr"]
  .product-image-section
  .vertical-slider
  > .slick-slider
  .slick-prev {
  left: 35% !important;
  transform: rotate(90deg) !important;
}
[dir="rtl"]
  .product-image-section
  .vertical-slider
  > .slick-slider
  .slick-prev {
  right: 35% !important;
  transform: rotate(-90deg) !important;
}
.product-image-section .vertical-slider > .slick-slider .slick-next {
  bottom: 0 !important;
}
[dir="ltr"]
  .product-image-section
  .vertical-slider
  > .slick-slider
  .slick-next {
  left: 35% !important;
  transform: rotate(90deg) !important;
}
[dir="rtl"]
  .product-image-section
  .vertical-slider
  > .slick-slider
  .slick-next {
  right: 35% !important;
  transform: rotate(-90deg) !important;
}
.product-image-section .slick-dots {
  position: absolute;
  bottom: 20px;
  display: grid;
  align-items: center;
  gap: 20px;
  width: 100%;
  grid-template-columns: 1fr 20px 1fr;
  pointer-events: none;
  list-style-type: none;
}
[dir] .product-image-section .slick-dots {
  margin: 0 auto;
  padding: 0;
}
[dir="ltr"] .product-image-section .slick-dots {
  transform: rotate(90deg) scale(-1);
}
[dir="rtl"] .product-image-section .slick-dots {
  transform: rotate(-90deg) scale(-1);
}
.product-image-section .slick-dots li {
  grid-column-start: 2;
  pointer-events: all;
}
[dir] .product-image-section .slick-dots li {
  cursor: pointer;
}
.product-image-section .slick-dots li:before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  position: absolute;
}
[dir] .product-image-section .slick-dots li:before {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 999px;
  border: 1px solid #fff;
}
[dir] .product-image-section .slick-dots li.slick-active:before {
  background: rgba(0, 0, 0, 0.5);
}
.product-image-section .slick-dots li button {
  display: none;
}
.product-image-section .preview {
  height: auto;
  max-height: 500px;
  max-width: 500px;
}
[dir] .product-image-section .preview {
  padding: 0;
  border: none;
}
.product-image-section .preview .preview-item,
.product-image-section .preview .slick-track {
  width: auto;
}
.product-image-section .preview .slick-arrow {
  text-indent: -9999em;
  text-transform: uppercase;
  position: absolute;
  top: 50%;
  z-index: 9999;
  display: inline-block;
  width: 10px;
  height: 10px;
}
[dir] .product-image-section .preview .slick-arrow {
  border: solid rgba(0, 0, 0, 0.65);
  padding: 3px;
}
[dir="ltr"] .product-image-section .preview .slick-arrow {
  border-width: 0 3px 3px 0;
}
[dir="rtl"] .product-image-section .preview .slick-arrow {
  border-width: 0 0 3px 3px;
}
[dir="ltr"] .product-image-section .preview .slick-next {
  right: 20px;
  transform: rotate(-45deg);
}
[dir="rtl"] .product-image-section .preview .slick-next {
  left: 20px;
  transform: rotate(45deg);
}
[dir="ltr"] .product-image-section .preview .slick-prev {
  left: 20px;
  transform: rotate(135deg);
}
[dir="rtl"] .product-image-section .preview .slick-prev {
  right: 20px;
  transform: rotate(-135deg);
}
.product-image-section .vertical-slider {
  position: relative;
}
[dir="ltr"] .product-image-section .vertical-slider .preview {
  margin-left: 100px;
}
[dir="rtl"] .product-image-section .vertical-slider .preview {
  margin-right: 100px;
}
.product-image-section .thumbnail-item {
  max-width: 88px;
}
.images-list-slider-container .slick-dots {
  position: absolute;
  bottom: 20px;
  display: flex;
  align-items: center;
  list-style: none;
}
[dir] .images-list-slider-container .slick-dots {
  padding: 0;
  margin: 0;
}
[dir="ltr"] .images-list-slider-container .slick-dots {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .images-list-slider-container .slick-dots {
  right: 50%;
  transform: translate(50%, -50%);
}
[dir="ltr"] .images-list-slider-container .slick-dots li:not(:last-child) {
  margin-right: 10px;
}
[dir="rtl"] .images-list-slider-container .slick-dots li:not(:last-child) {
  margin-left: 10px;
}
@media (max-width: 425px) {
  [dir="ltr"] .images-list-slider-container .slick-dots li:not(:last-child) {
    margin-right: 8px;
  }
  [dir="rtl"] .images-list-slider-container .slick-dots li:not(:last-child) {
    margin-left: 8px;
  }
}
.images-list-slider-container .slick-dots li button {
  display: block;
  font-size: 0;
  width: 12px;
  height: 12px;
  line-height: 12px;
  transition: background-color 0.25s;
}
[dir] .images-list-slider-container .slick-dots li button {
  background-color: #fff;
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.3);
  border: 1px solid #f0f0f0;
  border-radius: 50%;
}
@media (max-width: 425px) {
  .images-list-slider-container .slick-dots li button {
    width: 10px;
    height: 10px;
    line-height: 10px;
  }
}
[dir] .images-list-slider-container .slick-dots li.slick-active button {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
@media (max-width: 425px) {
  .images-list-slider-container .slick-dots {
    bottom: 5px;
  }
}
.images-list-slider-container .slick-arrow {
  position: absolute;
  top: 50%;
  color: #fff;
  z-index: 1;
  font-size: 0;
  width: 50px;
  height: 50px;
  line-height: 50px;
  transition: all 0.8s ease;
}
.images-list-slider-container .slick-arrow:before {
  font-family: FontAwesome;
  font-size: 35px;
}
[dir] .images-list-slider-container .slick-arrow.slick-prev {
  transform: translateY(-50%);
}
[dir="ltr"] .images-list-slider-container .slick-arrow.slick-prev {
  left: 40px;
}
[dir="rtl"] .images-list-slider-container .slick-arrow.slick-prev {
  right: 40px;
}
.images-list-slider-container .slick-arrow.slick-prev:before {
  content: "\F104";
}
[dir] .images-list-slider-container .slick-arrow.slick-next {
  transform: translateY(-50%);
}
[dir="ltr"] .images-list-slider-container .slick-arrow.slick-next {
  right: 40px;
}
[dir="rtl"] .images-list-slider-container .slick-arrow.slick-next {
  left: 40px;
}
.images-list-slider-container .slick-arrow.slick-next:before {
  content: "\F105";
}
.images-list-slider-container .slick-arrow.slick-disabled {
  opacity: 0.8;
}
[dir] .images-list-slider-container .slick-arrow.slick-disabled {
  cursor: not-allowed;
}
@media (max-width: 425px) {
  .images-list-slider-container .slick-arrow {
    display: none !important;
  }
}
[dir] .category-item {
  border: 1px solid #f0f0f0;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}
.category-item .category-thumbnail {
  display: block;
  position: relative;
  height: 0;
  overflow: hidden;
}
[dir] .category-item .category-thumbnail {
  padding: 0 0 100%;
}
.category-item .category-thumbnail img {
  position: absolute;
  top: 50%;
  max-height: 100%;
  text-indent: -9999px;
  transition: transform 0.25s ease;
}
[dir="ltr"] .category-item .category-thumbnail img {
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
}
[dir="rtl"] .category-item .category-thumbnail img {
  right: 50%;
  transform: translate(50%, -50%) scale(1);
}
.category-item .category-details {
  transition: border 0.25s;
}
[dir] .category-item .category-details {
  padding: 20px 15px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
.category-item .category-title {
  font-size: 14px;
  display: block;
  color: inherit;
}
[dir="ltr"] .category-item:hover .category-thumbnail img {
  transform: translate(-50%, -50%) scale(1.1);
}
[dir="rtl"] .category-item:hover .category-thumbnail img {
  transform: translate(50%, -50%) scale(1.1);
}
.categories-style-1 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 25px;
}
@media (max-width: 425px) and (max-width: 1124px) {
  .categories-style-1 {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 425px) and (max-width: 768px) {
  .categories-style-1 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 425px) {
  .categories-style-1 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.categories-style-1.sub-categories {
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 20px;
}
[dir] .categories-style-1.sub-categories {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  padding: 20px;
}
[dir] .categories-style-1.sub-categories .category-item {
  background-color: #fff;
}
@media (max-width: 425px) and (max-width: 1124px) {
  .categories-style-1.sub-categories {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 425px) and (max-width: 768px) {
  .categories-style-1.sub-categories {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 425px) {
  .categories-style-1.sub-categories {
    grid-template-columns: repeat(1, 1fr);
  }
}
[dir] .single-category .single-header {
  margin: 0 0 34px;
}
.single-category .single-header h1 {
  font-size: 26px;
  line-height: 1;
}
@media (max-width: 768px) {
  .single-category .single-header h1 {
    font-size: 22px;
  }
}
@media (max-width: 425px) {
  [dir] .single-category .single-header h1 {
    margin: 0 0 15px;
  }
}
.single-category .single-filters {
  display: flex;
}
[dir] .single-category .single-filters {
  margin: 0 0 24px;
}
@media (min-width: 425px) {
  .single-category .single-filters {
    align-items: flex-end;
    justify-content: space-between;
  }
}
@media (max-width: 425px) {
  .single-category .single-filters {
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
}
.single-category .single-body .more-products,
.single-category .single-body .single-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-category .single-body .single-loading {
  height: 20px;
}
[dir] .single-category .single-body .empty {
  text-align: center;
}
.single-category .single-body .empty h3 {
  font-size: 32px;
  font-weight: 600;
}
@media (max-width: 425px) {
  .single-category .single-body .empty h3 {
    font-size: 24px;
  }
}
[dir] .single-category .single-body .empty img {
  margin: 24px auto 0;
}
@media (min-width: 768px) {
  .single-category .single-body .empty img {
    max-width: 650px;
  }
}
.category-sort,
.category-sort ul {
  display: flex;
  align-items: center;
}
[dir] .category-sort ul {
  padding: 4px 0 0;
}
@media (max-width: 425px) {
  .category-sort {
    flex-direction: column;
  }
}
.category-sort h3 {
  font-size: 14px;
}
[dir="ltr"] .category-sort h3 {
  margin: 0 15px 0 0;
}
[dir="rtl"] .category-sort h3 {
  margin: 0 0 0 15px;
}
@media (max-width: 425px) {
  [dir] .category-sort h3 {
    margin: 0 0 10px;
  }
}
.category-sort a {
  display: flex;
  position: relative;
  font-size: 13px;
  color: inherit;
}
[dir] .category-sort a {
  cursor: pointer;
}
[dir="ltr"] .category-sort a {
  padding: 0 18px 0 0;
}
[dir="rtl"] .category-sort a {
  padding: 0 0 0 18px;
}
.category-sort a .yc {
  position: absolute;
  top: 4px;
}
[dir="ltr"] .category-sort a .yc {
  right: 0;
}
[dir="rtl"] .category-sort a .yc {
  left: 0;
}
.category-sort a.active {
  font-weight: 500;
  color: var(--primary-color);
}
[dir="ltr"] .category-sort li:not(:last-child) {
  margin: 0 18px 0 0;
}
[dir="rtl"] .category-sort li:not(:last-child) {
  margin: 0 0 0 18px;
}
.category-sort li.active a {
  color: var(--primary-color);
}
.categories-section .more-categories {
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 425px) {
  .content-box-with-icon-section .grid-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
[dir] .content-box-with-icon-section .content-box {
  text-align: center;
}
.content-box-with-icon-section .icon {
  display: block;
  height: 68px;
  width: 68px;
  line-height: 68px;
  font-size: 34px;
}
[dir] .content-box-with-icon-section .icon {
  border-radius: 50%;
  margin: 0 auto 15px;
}
[dir] .content-box-with-icon-section .title {
  margin: 0 0 5px;
}
.content-box-with-icon-section .description {
  word-break: break-word;
}
[dir] .content-box-with-icon-section .description {
  margin: 0 0 20px;
}
.fr-element,
.fr-element:focus {
  outline: 0 solid transparent;
}
.fr-box.fr-basic {
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-basic {
  border-radius: 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-box.fr-basic .fr-element {
  font-family: sans-serif;
  color: #414141;
  font-size: 14px;
  line-height: 1.6;
  box-sizing: border-box;
  overflow-x: auto;
  min-height: 60px;
}
[dir] .fr-box.fr-basic .fr-element {
  padding: 20px;
}
[dir="ltr"] .fr-box.fr-basic .fr-element {
  text-align: left;
}
[dir="ltr"] .fr-box.fr-basic.fr-rtl .fr-element,
[dir="rtl"] .fr-box.fr-basic .fr-element {
  text-align: right;
}
[dir="rtl"] .fr-box.fr-basic.fr-rtl .fr-element {
  text-align: left;
}
.fr-element {
  position: relative;
  z-index: 2;
  -webkit-user-select: auto;
}
[dir] .fr-element {
  background: transparent;
}
.fr-element a {
  user-select: auto;
  -o-user-select: auto;
  -moz-user-select: auto;
  -khtml-user-select: auto;
  -webkit-user-select: auto;
  -ms-user-select: auto;
}
.fr-element.fr-disabled {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.fr-element [contenteditable="true"] {
  outline: 0 solid transparent;
}
.fr-box a.fr-floating-btn {
  -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-background-clip: padding;
  height: 40px;
  width: 40px;
  color: #333;
  -moz-transition: background 0.2s ease 0s, color 0.2s ease 0s,
    transform 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s, color 0.2s ease 0s,
    transform 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s, color 0.2s ease 0s,
    transform 0.2s ease 0s;
  outline: none;
  top: 0;
  line-height: 40px;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  display: block;
  box-sizing: border-box;
}
[dir] .fr-box a.fr-floating-btn {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
  border-radius: 100%;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background: #fff;
  -webkit-transition: background 0.2s ease 0s, color 0.2s ease 0s,
    transform 0.2s ease 0s;
  text-align: center;
  border: none;
}
[dir="ltr"] .fr-box a.fr-floating-btn {
  left: 0;
}
[dir="rtl"] .fr-box a.fr-floating-btn {
  right: 0;
}
.fr-box a.fr-floating-btn svg {
  -moz-transition: transform 0.2s ease 0s;
  -ms-transition: transform 0.2s ease 0s;
  -o-transition: transform 0.2s ease 0s;
  fill: #333;
}
[dir] .fr-box a.fr-floating-btn svg {
  -webkit-transition: transform 0.2s ease 0s;
}
.fr-box a.fr-floating-btn i,
.fr-box a.fr-floating-btn svg {
  font-size: 14px;
  line-height: 40px;
}
[dir="ltr"] .fr-box a.fr-floating-btn.fr-btn + .fr-btn {
  margin-left: 10px;
}
[dir="rtl"] .fr-box a.fr-floating-btn.fr-btn + .fr-btn {
  margin-right: 10px;
}
[dir] .fr-box a.fr-floating-btn:hover {
  background: #ebebeb;
  cursor: pointer;
}
.fr-box a.fr-floating-btn:hover svg {
  fill: #333;
}
.fr-box .fr-visible a.fr-floating-btn {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
}
iframe.fr-iframe {
  width: 100%;
  position: relative;
  display: block;
  z-index: 2;
  box-sizing: border-box;
}
[dir] iframe.fr-iframe {
  border: none;
}
.fr-wrapper {
  position: relative;
  z-index: 1;
}
.fr-wrapper:after {
  display: block;
  content: "";
  height: 0;
}
[dir] .fr-wrapper:after {
  clear: both;
}
.fr-wrapper .fr-placeholder {
  position: absolute;
  font-size: 14px;
  color: #aaa;
  font-family: sans-serif;
  z-index: 1;
  display: none;
  top: 0;
  overflow: hidden;
}
[dir="ltr"] .fr-wrapper .fr-placeholder,
[dir="rtl"] .fr-wrapper .fr-placeholder {
  left: 0;
  right: 0;
}
.fr-wrapper.show-placeholder .fr-placeholder {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.fr-wrapper ::-moz-selection {
  color: #000;
}
.fr-wrapper ::selection {
  color: #000;
}
[dir] .fr-wrapper ::-moz-selection {
  background: #b5d6fd;
}
[dir] .fr-wrapper ::selection {
  background: #b5d6fd;
}
.fr-box.fr-basic .fr-wrapper {
  top: 0;
}
[dir] .fr-box.fr-basic .fr-wrapper {
  background: #fff;
  border: 1px solid;
  border-color: #ccc #ccc #efefef;
}
[dir="ltr"] .fr-box.fr-basic .fr-wrapper {
  left: 0;
}
[dir="rtl"] .fr-box.fr-basic .fr-wrapper {
  right: 0;
}
[dir] .fr-box.fr-basic.fr-top .fr-wrapper {
  border-top: 0;
}
.fr-box.fr-basic.fr-bottom .fr-wrapper {
  -moz-border-radius: 10px 10px 0 0;
  -webkit-border-radius: 10px 10px 0 0;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-basic.fr-bottom .fr-wrapper {
  border-bottom: 0;
  border-radius: 10px 10px 0 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
@media (min-width: 992px) {
  .fr-box.fr-document,
  .fr-box.fr-document .fr-wrapper {
    min-width: 21cm;
  }
  [dir] .fr-box.fr-document .fr-wrapper {
    padding: 30px;
    background: #efefef;
  }
  [dir="ltr"] .fr-box.fr-document .fr-wrapper {
    text-align: left;
  }
  [dir="rtl"] .fr-box.fr-document .fr-wrapper {
    text-align: right;
  }
  .fr-box.fr-document .fr-wrapper .fr-element {
    width: 21cm;
    min-height: 26cm !important;
    overflow: visible;
    z-index: auto;
  }
  [dir] .fr-box.fr-document .fr-wrapper .fr-element {
    background: #fff;
    margin: auto;
    padding: 1cm 2cm;
  }
  [dir="ltr"] .fr-box.fr-document .fr-wrapper .fr-element {
    text-align: left;
  }
  [dir="rtl"] .fr-box.fr-document .fr-wrapper .fr-element {
    text-align: right;
  }
  .fr-box.fr-document .fr-wrapper .fr-element hr {
    height: 1cm;
    outline: none;
  }
  [dir] .fr-box.fr-document .fr-wrapper .fr-element hr {
    background: #efefef;
    border: none;
  }
  [dir="ltr"] .fr-box.fr-document .fr-wrapper .fr-element hr,
  [dir="rtl"] .fr-box.fr-document .fr-wrapper .fr-element hr {
    margin-left: -2cm;
    margin-right: -2cm;
  }
  .fr-box.fr-document .fr-wrapper .fr-element img {
    z-index: 1;
  }
}
.fr-tooltip {
  position: absolute;
  top: 0;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  color: #fff;
  font-size: 11px;
  line-height: 22px;
  font-family: Arial, Helvetica, sans-serif;
  -moz-transition: opacity 0.2s ease 0s;
  -ms-transition: opacity 0.2s ease 0s;
  -o-transition: opacity 0.2s ease 0s;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  z-index: 2147483647;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[dir] .fr-tooltip {
  padding: 0 8px;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background: #222;
  -webkit-transition: opacity 0.2s ease 0s;
}
[dir="ltr"] .fr-tooltip {
  left: 0;
  left: -3000px;
}
[dir="rtl"] .fr-tooltip {
  right: 0;
  right: -3000px;
}
.fr-tooltip.fr-visible {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-modal .fr-btn-wrap,
.fr-popup .fr-btn-wrap,
.fr-toolbar .fr-btn-wrap {
  white-space: nowrap;
  position: relative;
}
[dir="ltr"] .fr-modal .fr-btn-wrap,
[dir="ltr"] .fr-popup .fr-btn-wrap,
[dir="ltr"] .fr-toolbar .fr-btn-wrap {
  float: left;
}
[dir="rtl"] .fr-modal .fr-btn-wrap,
[dir="rtl"] .fr-popup .fr-btn-wrap,
[dir="rtl"] .fr-toolbar .fr-btn-wrap {
  float: right;
}
.fr-modal .fr-btn-wrap.fr-hidden,
.fr-popup .fr-btn-wrap.fr-hidden,
.fr-toolbar .fr-btn-wrap.fr-hidden {
  display: none;
}
.fr-modal .fr-command.fr-btn,
.fr-popup .fr-command.fr-btn,
.fr-toolbar .fr-command.fr-btn {
  color: #333;
  -moz-outline: 0;
  outline: none;
  line-height: 1;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-background-clip: padding;
  z-index: 2;
  position: relative;
  box-sizing: border-box;
  text-decoration: none;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  height: 40px;
}
[dir] .fr-modal .fr-command.fr-btn,
[dir] .fr-popup .fr-command.fr-btn,
[dir] .fr-toolbar .fr-command.fr-btn {
  background: transparent;
  border: 0;
  cursor: pointer;
  margin: 4px 2px;
  padding: 0;
  -webkit-transition: all 0.5s;
  border-radius: 4px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn,
[dir="ltr"] .fr-popup .fr-command.fr-btn,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn {
  text-align: left;
  float: left;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn,
[dir="rtl"] .fr-popup .fr-command.fr-btn,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn {
  text-align: right;
  float: right;
}
.fr-modal .fr-command.fr-btn.fr-dropdown.fr-options,
.fr-popup .fr-command.fr-btn.fr-dropdown.fr-options,
.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-options {
  -moz-background-clip: padding;
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-options,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-options,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-options {
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-options {
  border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  -webkit-border-radius: 0 4px 4px 0;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-options {
  border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  -webkit-border-radius: 4px 0 0 4px;
}
.fr-modal .fr-command.fr-btn.fr-btn-hover,
.fr-popup .fr-command.fr-btn.fr-btn-hover,
.fr-toolbar .fr-command.fr-btn.fr-btn-hover {
  -moz-background-clip: padding;
}
[dir] .fr-modal .fr-command.fr-btn.fr-btn-hover,
[dir] .fr-popup .fr-command.fr-btn.fr-btn-hover,
[dir] .fr-toolbar .fr-command.fr-btn.fr-btn-hover {
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-btn-hover,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-btn-hover,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-btn-hover {
  border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  -webkit-border-radius: 4px 0 0 4px;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-btn-hover,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-btn-hover,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-btn-hover {
  border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  -webkit-border-radius: 0 4px 4px 0;
}
[dir] .fr-modal .fr-command.fr-btn::-moz-focus-inner,
[dir] .fr-popup .fr-command.fr-btn::-moz-focus-inner,
[dir] .fr-toolbar .fr-command.fr-btn::-moz-focus-inner {
  border: 0;
  padding: 0;
}
.fr-modal .fr-command.fr-btn.fr-btn-text,
.fr-popup .fr-command.fr-btn.fr-btn-text,
.fr-toolbar .fr-command.fr-btn.fr-btn-text {
  width: auto;
}
.fr-modal .fr-command.fr-btn i,
.fr-modal .fr-command.fr-btn svg,
.fr-popup .fr-command.fr-btn i,
.fr-popup .fr-command.fr-btn svg,
.fr-toolbar .fr-command.fr-btn i,
.fr-toolbar .fr-command.fr-btn svg {
  display: block;
  width: 24px;
}
[dir] .fr-modal .fr-command.fr-btn i,
[dir] .fr-modal .fr-command.fr-btn svg,
[dir] .fr-popup .fr-command.fr-btn i,
[dir] .fr-popup .fr-command.fr-btn svg,
[dir] .fr-toolbar .fr-command.fr-btn i,
[dir] .fr-toolbar .fr-command.fr-btn svg {
  text-align: center;
  float: none;
  margin: 8px 7px;
}
.fr-modal .fr-command.fr-btn svg.fr-svg,
.fr-popup .fr-command.fr-btn svg.fr-svg,
.fr-toolbar .fr-command.fr-btn svg.fr-svg {
  height: 24px;
}
.fr-modal .fr-command.fr-btn svg path,
.fr-popup .fr-command.fr-btn svg path,
.fr-toolbar .fr-command.fr-btn svg path {
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  fill: #333;
}
[dir] .fr-modal .fr-command.fr-btn svg path,
[dir] .fr-popup .fr-command.fr-btn svg path,
[dir] .fr-toolbar .fr-command.fr-btn svg path {
  -webkit-transition: all 0.5s;
}
.fr-modal .fr-command.fr-btn span.fr-sr-only,
.fr-popup .fr-command.fr-btn span.fr-sr-only,
.fr-toolbar .fr-command.fr-btn span.fr-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
[dir] .fr-modal .fr-command.fr-btn span.fr-sr-only,
[dir] .fr-popup .fr-command.fr-btn span.fr-sr-only,
[dir] .fr-toolbar .fr-command.fr-btn span.fr-sr-only {
  padding: 0;
  margin: -1px;
  border: 0;
}
.fr-modal .fr-command.fr-btn span,
.fr-popup .fr-command.fr-btn span,
.fr-toolbar .fr-command.fr-btn span {
  font-size: 14px;
  display: block;
  line-height: 17px;
  min-width: 30px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  height: 17px;
  font-weight: 700;
}
[dir] .fr-modal .fr-command.fr-btn span,
[dir] .fr-popup .fr-command.fr-btn span,
[dir] .fr-toolbar .fr-command.fr-btn span {
  padding: 0 4px;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn span,
[dir="ltr"] .fr-popup .fr-command.fr-btn span,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn span {
  float: left;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn span,
[dir="rtl"] .fr-popup .fr-command.fr-btn span,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn span {
  float: right;
}
.fr-modal .fr-command.fr-btn img,
.fr-popup .fr-command.fr-btn img,
.fr-toolbar .fr-command.fr-btn img {
  width: 24px;
}
[dir] .fr-modal .fr-command.fr-btn img,
[dir] .fr-popup .fr-command.fr-btn img,
[dir] .fr-toolbar .fr-command.fr-btn img {
  margin: 8px 7px;
}
[dir] .fr-modal .fr-command.fr-btn.fr-btn-active-popup,
[dir] .fr-popup .fr-command.fr-btn.fr-btn-active-popup,
[dir] .fr-toolbar .fr-command.fr-btn.fr-btn-active-popup {
  background: #d6d6d6;
}
.fr-modal .fr-command.fr-btn.fr-dropdown.fr-selection span,
.fr-popup .fr-command.fr-btn.fr-dropdown.fr-selection span,
.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-selection span {
  font-weight: 400;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown i,
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown img,
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown span,
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown svg,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown i,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown img,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown span,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown svg,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown i,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown img,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown span,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown svg {
  margin-left: 3px;
  margin-right: 11px;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown i,
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown img,
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown span,
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown svg,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown i,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown img,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown span,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown svg,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown i,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown img,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown span,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown svg {
  margin-right: 3px;
  margin-left: 11px;
}
.fr-modal .fr-command.fr-btn.fr-dropdown:after,
.fr-popup .fr-command.fr-btn.fr-dropdown:after,
.fr-toolbar .fr-command.fr-btn.fr-dropdown:after {
  position: absolute;
  width: 0;
  height: 0;
  top: 18px;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  content: "";
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown:after,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown:after,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown:after {
  border-top: 4px solid #333;
  -webkit-transition: all 0.3s;
}
[dir="ltr"] .fr-modal .fr-command.fr-btn.fr-dropdown:after,
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-dropdown:after,
[dir="ltr"] .fr-toolbar .fr-command.fr-btn.fr-dropdown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  right: 2px;
}
[dir="rtl"] .fr-modal .fr-command.fr-btn.fr-dropdown:after,
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-dropdown:after,
[dir="rtl"] .fr-toolbar .fr-command.fr-btn.fr-dropdown:after {
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  left: 2px;
}
.fr-modal .fr-command.fr-btn.fr-dropdown.fr-active,
.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active,
.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active {
  fill: #333;
  -moz-transition: 0.5s ease;
  -ms-transition: 0.5s ease;
  -o-transition: 0.5s ease;
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-active,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-active,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active {
  background: #d6d6d6;
  -webkit-transition: 0.5s ease;
}
.fr-modal .fr-command.fr-btn.fr-dropdown.fr-active:hover,
.fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:hover,
.fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:hover {
  fill: #333;
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-active:hover,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:hover,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:hover {
  background: #ebebeb;
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-active:hover:after,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:hover:after,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:hover:after {
  border-top-color: #333;
}
[dir] .fr-modal .fr-command.fr-btn.fr-dropdown.fr-active:after,
[dir] .fr-popup .fr-command.fr-btn.fr-dropdown.fr-active:after,
[dir] .fr-toolbar .fr-command.fr-btn.fr-dropdown.fr-active:after {
  border-top: 0;
  border-bottom: 4px solid #222;
}
.fr-modal .fr-command.fr-btn.fr-disabled,
.fr-popup .fr-command.fr-btn.fr-disabled,
.fr-toolbar .fr-command.fr-btn.fr-disabled {
  color: #bdbdbd;
  -webkit-opacity: 0.3;
  -moz-opacity: 0.3;
  opacity: 0.3;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
[dir] .fr-modal .fr-command.fr-btn.fr-disabled,
[dir] .fr-popup .fr-command.fr-btn.fr-disabled,
[dir] .fr-toolbar .fr-command.fr-btn.fr-disabled {
  cursor: default;
}
[dir] .fr-modal .fr-command.fr-btn.fr-disabled:after,
[dir] .fr-popup .fr-command.fr-btn.fr-disabled:after,
[dir] .fr-toolbar .fr-command.fr-btn.fr-disabled:after {
  border-top-color: #bdbdbd;
}
.fr-modal .fr-command.fr-btn.fr-hidden,
.fr-popup .fr-command.fr-btn.fr-hidden,
.fr-toolbar .fr-command.fr-btn.fr-hidden {
  display: none;
}
.fr-modal .fr-tabs .fr-command.fr-btn,
.fr-popup .fr-tabs .fr-command.fr-btn,
.fr-toolbar .fr-tabs .fr-command.fr-btn {
  width: 46px;
  height: 40px;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-modal .fr-tabs .fr-command.fr-btn,
[dir] .fr-popup .fr-tabs .fr-command.fr-btn,
[dir] .fr-toolbar .fr-tabs .fr-command.fr-btn {
  margin: 0;
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir] .fr-modal .fr-tabs .fr-command.fr-btn:not(:hover):not(:focus).fr-active,
[dir]
  .fr-modal
  .fr-tabs
  .fr-command.fr-btn:not(:hover):not(:focus).fr-active-tab,
[dir] .fr-popup .fr-tabs .fr-command.fr-btn:not(:hover):not(:focus).fr-active,
[dir]
  .fr-popup
  .fr-tabs
  .fr-command.fr-btn:not(:hover):not(:focus).fr-active-tab,
[dir] .fr-toolbar .fr-tabs .fr-command.fr-btn:not(:hover):not(:focus).fr-active,
[dir]
  .fr-toolbar
  .fr-tabs
  .fr-command.fr-btn:not(:hover):not(:focus).fr-active-tab {
  background: #fff;
}
.fr-modal .fr-tabs .fr-command.fr-btn span,
.fr-popup .fr-tabs .fr-command.fr-btn span,
.fr-toolbar .fr-tabs .fr-command.fr-btn span {
  height: 27px;
}
.fr-modal .fr-tabs .fr-command.fr-btn img,
.fr-modal .fr-tabs .fr-command.fr-btn svg,
.fr-popup .fr-tabs .fr-command.fr-btn img,
.fr-popup .fr-tabs .fr-command.fr-btn svg,
.fr-toolbar .fr-tabs .fr-command.fr-btn img,
.fr-toolbar .fr-tabs .fr-command.fr-btn svg {
  width: 24px;
  height: 24px;
}
[dir] .fr-modal .fr-tabs .fr-command.fr-btn img,
[dir] .fr-modal .fr-tabs .fr-command.fr-btn svg,
[dir] .fr-popup .fr-tabs .fr-command.fr-btn img,
[dir] .fr-popup .fr-tabs .fr-command.fr-btn svg,
[dir] .fr-toolbar .fr-tabs .fr-command.fr-btn img,
[dir] .fr-toolbar .fr-tabs .fr-command.fr-btn svg {
  margin: 8px 11px;
}
.fr-modal .fr-btn-grp .fr-command.fr-btn.fr-active:not(.fr-dropdown) svg path,
.fr-modal
  .fr-buttons:not(.fr-tabs)
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path,
.fr-modal
  .fr-more-toolbar
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path,
.fr-popup .fr-btn-grp .fr-command.fr-btn.fr-active:not(.fr-dropdown) svg path,
.fr-popup
  .fr-buttons:not(.fr-tabs)
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path,
.fr-popup
  .fr-more-toolbar
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path,
.fr-toolbar .fr-btn-grp .fr-command.fr-btn.fr-active:not(.fr-dropdown) svg path,
.fr-toolbar
  .fr-buttons:not(.fr-tabs)
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path,
.fr-toolbar
  .fr-more-toolbar
  .fr-command.fr-btn.fr-active:not(.fr-dropdown)
  svg
  path {
  fill: #0098f7;
}
.fr-modal.fr-disabled .fr-btn,
.fr-modal.fr-disabled .fr-btn.fr-active,
.fr-popup.fr-disabled .fr-btn,
.fr-popup.fr-disabled .fr-btn.fr-active,
.fr-toolbar.fr-disabled .fr-btn,
.fr-toolbar.fr-disabled .fr-btn.fr-active {
  color: #bdbdbd;
  -webkit-opacity: 0.3;
  -moz-opacity: 0.3;
  opacity: 0.3;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
[dir] .fr-modal.fr-disabled .fr-btn.fr-active.fr-dropdown:after,
[dir] .fr-modal.fr-disabled .fr-btn.fr-dropdown:after,
[dir] .fr-popup.fr-disabled .fr-btn.fr-active.fr-dropdown:after,
[dir] .fr-popup.fr-disabled .fr-btn.fr-dropdown:after,
[dir] .fr-toolbar.fr-disabled .fr-btn.fr-active.fr-dropdown:after,
[dir] .fr-toolbar.fr-disabled .fr-btn.fr-dropdown:after {
  border-top-color: #bdbdbd;
}
[dir="ltr"] .fr-modal.fr-rtl .fr-btn-grp.fr-float-left,
[dir="ltr"] .fr-popup.fr-rtl .fr-btn-grp.fr-float-left,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-btn-grp.fr-float-left {
  float: right;
}
[dir="ltr"] .fr-modal.fr-rtl .fr-btn-grp.fr-float-right,
[dir="ltr"] .fr-popup.fr-rtl .fr-btn-grp.fr-float-right,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-btn-grp.fr-float-right,
[dir="rtl"] .fr-modal.fr-rtl .fr-btn-grp.fr-float-left,
[dir="rtl"] .fr-popup.fr-rtl .fr-btn-grp.fr-float-left,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-btn-grp.fr-float-left {
  float: left;
}
[dir="ltr"] .fr-modal.fr-rtl .fr-btn-wrap,
[dir="ltr"] .fr-modal.fr-rtl .fr-command.fr-btn,
[dir="ltr"] .fr-popup.fr-rtl .fr-btn-wrap,
[dir="ltr"] .fr-popup.fr-rtl .fr-command.fr-btn,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-btn-wrap,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-command.fr-btn,
[dir="rtl"] .fr-modal.fr-rtl .fr-btn-grp.fr-float-right,
[dir="rtl"] .fr-popup.fr-rtl .fr-btn-grp.fr-float-right,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-btn-grp.fr-float-right {
  float: right;
}
[dir="rtl"] .fr-modal.fr-rtl .fr-btn-wrap,
[dir="rtl"] .fr-modal.fr-rtl .fr-command.fr-btn,
[dir="rtl"] .fr-popup.fr-rtl .fr-btn-wrap,
[dir="rtl"] .fr-popup.fr-rtl .fr-command.fr-btn,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-btn-wrap,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-command.fr-btn {
  float: left;
}
.fr-modal.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
.fr-modal.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
.fr-popup.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
.fr-popup.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
.fr-toolbar.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
.fr-toolbar.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options {
  -moz-background-clip: padding;
}
[dir] .fr-modal.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir] .fr-modal.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir] .fr-popup.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir] .fr-popup.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options {
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-modal.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="ltr"] .fr-modal.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="ltr"] .fr-popup.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="ltr"] .fr-popup.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options {
  border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  -webkit-border-radius: 4px 0 0 4px;
}
[dir="rtl"] .fr-modal.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="rtl"] .fr-modal.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="rtl"] .fr-popup.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="rtl"] .fr-popup.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-dropdown.fr-options,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-dropdown.fr-options {
  border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  -webkit-border-radius: 0 4px 4px 0;
}
.fr-modal.fr-rtl .fr-btn-wrap.fr-btn-hover,
.fr-modal.fr-rtl .fr-command.fr-btn.fr-btn-hover,
.fr-popup.fr-rtl .fr-btn-wrap.fr-btn-hover,
.fr-popup.fr-rtl .fr-command.fr-btn.fr-btn-hover,
.fr-toolbar.fr-rtl .fr-btn-wrap.fr-btn-hover,
.fr-toolbar.fr-rtl .fr-command.fr-btn.fr-btn-hover {
  -moz-background-clip: padding;
}
[dir] .fr-modal.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir] .fr-modal.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir] .fr-popup.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir] .fr-popup.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-btn-hover {
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-modal.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="ltr"] .fr-modal.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir="ltr"] .fr-popup.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="ltr"] .fr-popup.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-btn-hover {
  border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  -webkit-border-radius: 0 4px 4px 0;
}
[dir="rtl"] .fr-modal.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="rtl"] .fr-modal.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir="rtl"] .fr-popup.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="rtl"] .fr-popup.fr-rtl .fr-command.fr-btn.fr-btn-hover,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-btn-wrap.fr-btn-hover,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-command.fr-btn.fr-btn-hover {
  border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  -webkit-border-radius: 4px 0 0 4px;
}
.fr-toolbar.fr-inline > .fr-btn-wrap:not(.fr-hidden),
.fr-toolbar.fr-inline > .fr-command.fr-btn:not(.fr-hidden) {
  display: inline-flex;
}
[dir] .fr-toolbar.fr-inline > .fr-btn-wrap:not(.fr-hidden),
[dir] .fr-toolbar.fr-inline > .fr-command.fr-btn:not(.fr-hidden) {
  float: none;
}
.fr-desktop .fr-command.fr-btn-hover,
.fr-desktop .fr-command.fr-expanded,
.fr-desktop .fr-command:focus,
.fr-desktop .fr-command:hover {
  outline: 0;
  color: #333;
}
[dir] .fr-desktop .fr-command.fr-btn-hover:not(.fr-table-cell),
[dir] .fr-desktop .fr-command.fr-expanded:not(.fr-table-cell),
[dir] .fr-desktop .fr-command:focus:not(.fr-table-cell),
[dir] .fr-desktop .fr-command:hover:not(.fr-table-cell) {
  background: #ebebeb;
}
[dir] .fr-desktop .fr-command.fr-btn-hover:after,
[dir] .fr-desktop .fr-command.fr-expanded:after,
[dir] .fr-desktop .fr-command:focus:after,
[dir] .fr-desktop .fr-command:hover:after {
  border-top-color: #333;
}
.fr-desktop .fr-command.fr-selected:not(.fr-table-cell),
.fr-desktop .fr-command:active {
  color: #333;
}
[dir] .fr-desktop .fr-command.fr-selected:not(.fr-table-cell),
[dir] .fr-desktop .fr-command:active {
  background: #d6d6d6;
}
[dir] .fr-desktop .fr-command.fr-active.fr-btn-hover,
[dir] .fr-desktop .fr-command.fr-active.fr-expanded,
[dir] .fr-desktop .fr-command.fr-active:focus,
[dir] .fr-desktop .fr-command.fr-active:hover {
  background: #ebebeb;
}
[dir] .fr-desktop .fr-command.fr-active:active {
  background: #d6d6d6;
}
[dir] .fr-desktop .fr-command.fr-disabled.fr-selected,
[dir] .fr-desktop .fr-command.fr-disabled:focus,
[dir] .fr-desktop .fr-command.fr-disabled:hover,
[dir] .fr-desktop.fr-disabled .fr-command.fr-selected,
[dir] .fr-desktop.fr-disabled .fr-command:focus,
[dir] .fr-desktop.fr-disabled .fr-command:hover {
  background: transparent;
}
[dir] .fr-popup.fr-mobile .fr-command.fr-blink,
[dir] .fr-toolbar.fr-mobile .fr-command.fr-blink {
  background: #d6d6d6;
}
.fr-command.fr-btn.fr-options {
  width: 16px;
}
[dir="ltr"] .fr-command.fr-btn.fr-options {
  margin-left: -5px;
}
[dir="rtl"] .fr-command.fr-btn.fr-options {
  margin-right: -5px;
}
[dir="ltr"] .fr-command.fr-btn.fr-options.fr-btn-hover,
[dir="ltr"] .fr-command.fr-btn.fr-options:focus,
[dir="ltr"] .fr-command.fr-btn.fr-options:hover {
  border-left: 1px solid #fafafa;
  -webkit-transition: border-left 0s, background-color 0.5s;
  -moz-transition: border-left 0s, background-color 0.5s;
  -ms-transition: border-left 0s, background-color 0.5s;
  -o-transition: border-left 0s, background-color 0.5s;
}
[dir="rtl"] .fr-command.fr-btn.fr-options.fr-btn-hover,
[dir="rtl"] .fr-command.fr-btn.fr-options:focus,
[dir="rtl"] .fr-command.fr-btn.fr-options:hover {
  border-right: 1px solid #fafafa;
  -webkit-transition: border-right 0s, background-color 0.5s;
  -moz-transition: border-right 0s, background-color 0.5s;
  -ms-transition: border-right 0s, background-color 0.5s;
  -o-transition: border-right 0s, background-color 0.5s;
}
.fr-command.fr-btn + .fr-dropdown-menu {
  display: inline-block;
  position: absolute;
  bottom: auto;
  height: auto;
  z-index: 4;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-background-clip: padding;
  -webkit-overflow-scrolling: touch;
  overflow: hidden;
  zoom: 1;
  -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
[dir] .fr-command.fr-btn + .fr-dropdown-menu {
  background: #fff;
  border-radius: 4px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
[dir="ltr"] .fr-command.fr-btn + .fr-dropdown-menu {
  right: auto;
}
[dir="rtl"] .fr-command.fr-btn + .fr-dropdown-menu {
  left: auto;
}
.fr-command.fr-btn + .fr-dropdown-menu.test-height .fr-dropdown-wrapper {
  -moz-transition: none;
  -ms-transition: none;
  -o-transition: none;
  height: auto;
  max-height: 275px;
}
[dir] .fr-command.fr-btn + .fr-dropdown-menu.test-height .fr-dropdown-wrapper {
  -webkit-transition: none;
}
.fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  -moz-transition: height 0.3s;
  -ms-transition: height 0.3s;
  -o-transition: height 0.3s;
  height: 0;
}
[dir] .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper {
  padding: 0;
  -webkit-transition: height 0.3s;
  margin: 0 auto auto;
  margin-top: 0 !important;
}
[dir="ltr"] .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper {
  text-align: left;
  float: left;
}
[dir="rtl"] .fr-command.fr-btn + .fr-dropdown-menu .fr-dropdown-wrapper {
  text-align: right;
  float: right;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content {
  position: relative;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list {
  list-style-type: none;
  min-width: 72px;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list {
  margin: 0;
  padding: 8px 0;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li {
  font-size: 15px;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li {
  padding: 0;
  margin: 0;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a {
  line-height: 200%;
  display: flex;
  white-space: nowrap;
  color: inherit;
  text-decoration: none;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a {
  padding: 0 20px;
  cursor: pointer;
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  svg {
  width: 24px;
  height: 24px;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  svg {
  margin: 3px 4px;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  svg
  path {
  fill: #333;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a.fr-active {
  background: #d6d6d6;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a.fr-disabled {
  color: #bdbdbd;
}
[dir]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a.fr-disabled {
  cursor: default;
}
.fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  .fr-shortcut {
  font-weight: 700;
  -webkit-opacity: 0.75;
  -moz-opacity: 0.75;
  opacity: 0.75;
}
[dir="ltr"]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  .fr-shortcut {
  margin-left: 20px;
}
[dir="rtl"]
  .fr-command.fr-btn
  + .fr-dropdown-menu
  .fr-dropdown-wrapper
  .fr-dropdown-content
  ul.fr-dropdown-list
  li
  a
  .fr-shortcut {
  margin-right: 20px;
}
.fr-command.fr-btn.fr-active + .fr-dropdown-menu {
  display: inline-block;
  -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2),
    0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
[dir] .fr-command.fr-btn.fr-active + .fr-dropdown-menu {
  box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
    0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.fr-bottom > .fr-command.fr-btn + .fr-dropdown-menu {
  -moz-border-radius: 2px 2px 0 0;
  -webkit-border-radius: 2px 2px 0 0;
  -moz-background-clip: padding;
}
[dir] .fr-bottom > .fr-command.fr-btn + .fr-dropdown-menu {
  border-radius: 2px 2px 0 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-popup.fr-rtl .fr-dropdown-wrapper,
[dir="ltr"] .fr-toolbar.fr-rtl .fr-dropdown-wrapper {
  text-align: right !important;
}
[dir="rtl"] .fr-popup.fr-rtl .fr-dropdown-wrapper,
[dir="rtl"] .fr-toolbar.fr-rtl .fr-dropdown-wrapper {
  text-align: left !important;
}
body.prevent-scroll {
  overflow: hidden;
}
body.prevent-scroll.fr-mobile {
  position: fixed;
  -webkit-overflow-scrolling: touch;
}
.fr-modal {
  color: #222;
  font-family: Arial, Helvetica, sans-serif;
  position: fixed;
  overflow-x: auto;
  overflow-y: scroll;
  top: 0;
  bottom: 0;
  width: 100%;
  z-index: 2147483640;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.2;
}
[dir] .fr-modal {
  text-align: center;
}
[dir="ltr"] .fr-modal,
[dir="rtl"] .fr-modal {
  left: 0;
  right: 0;
}
.fr-modal.fr-middle .fr-modal-wrapper {
  top: 50%;
  position: absolute;
}
[dir] .fr-modal.fr-middle .fr-modal-wrapper {
  margin-top: 0;
  margin-bottom: 0;
}
[dir="ltr"] .fr-modal.fr-middle .fr-modal-wrapper {
  margin-left: auto;
  margin-right: auto;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}
[dir="rtl"] .fr-modal.fr-middle .fr-modal-wrapper {
  margin-right: auto;
  margin-left: auto;
  right: 50%;
  -webkit-transform: translate(50%, -50%);
  -moz-transform: translate(50%, -50%);
  -ms-transform: translate(50%, -50%);
  -o-transform: translate(50%, -50%);
}
.fr-modal .fr-modal-wrapper {
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-background-clip: padding;
  display: inline-block;
  min-width: 300px;
  -webkit-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  overflow: hidden;
  width: 90%;
  position: relative;
}
[dir] .fr-modal .fr-modal-wrapper {
  border-radius: 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  margin: 20px auto;
  background: #fff;
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  border: 0;
}
@media (min-width: 768px) and (max-width: 991px) {
  .fr-modal .fr-modal-wrapper {
    width: 70%;
  }
  [dir] .fr-modal .fr-modal-wrapper {
    margin: 30px auto;
  }
}
@media (min-width: 992px) {
  .fr-modal .fr-modal-wrapper {
    width: 960px;
  }
  [dir] .fr-modal .fr-modal-wrapper {
    margin: 50px auto;
  }
}
.fr-modal .fr-modal-wrapper .fr-modal-head {
  overflow: hidden;
  position: absolute;
  width: 100%;
  min-height: 42px;
  z-index: 3;
  -moz-transition: all 0.2s ease 0s;
  -ms-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-head {
  background: #fff;
  border-bottom: 1px solid #efefef;
  -webkit-transition: all 0.2s ease 0s;
}
.fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-head-line {
  height: 56px;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-head-line {
  padding: 0 10px;
}
.fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-close {
  position: absolute;
  top: 0;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-close {
  margin: 10px;
}
[dir="ltr"] .fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-close {
  right: 0;
}
[dir="rtl"] .fr-modal .fr-modal-wrapper .fr-modal-head .fr-modal-close {
  left: 0;
}
.fr-modal .fr-modal-wrapper .fr-modal-head h4 {
  font-size: 20px;
  font-weight: 400;
  line-height: 18px;
  display: inline-block;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-head h4 {
  padding: 19px 10px;
  margin: 0;
}
[dir="ltr"] .fr-modal .fr-modal-wrapper .fr-modal-head h4 {
  float: left;
}
[dir="rtl"] .fr-modal .fr-modal-wrapper .fr-modal-head h4 {
  float: right;
}
.fr-modal .fr-modal-wrapper div.fr-modal-body {
  height: 100%;
  min-height: 150px;
  overflow-y: auto;
}
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body {
  padding-bottom: 20px;
}
.fr-modal .fr-modal-wrapper div.fr-modal-body:focus {
  outline: 0;
}
.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command {
  height: 36px;
  line-height: 1;
  color: #0098f7;
  text-decoration: none;
  font-size: 16px;
  outline: none;
  -moz-transition: background 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
}
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command {
  padding: 10px;
  cursor: pointer;
  border: none;
  background: none;
  -webkit-transition: background 0.2s ease 0s;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"]
  .fr-modal
  .fr-modal-wrapper
  div.fr-modal-body
  button.fr-command
  + button {
  margin-left: 24px;
}
[dir="rtl"]
  .fr-modal
  .fr-modal-wrapper
  div.fr-modal-body
  button.fr-command
  + button {
  margin-right: 24px;
}
.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:focus,
.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:hover {
  color: #0098f7;
}
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:focus,
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:hover {
  background: #ebebeb;
}
.fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:active {
  color: #0098f7;
}
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body button.fr-command:active {
  background: #d6d6d6;
}
[dir] .fr-modal .fr-modal-wrapper div.fr-modal-body button::-moz-focus-inner {
  border: 0;
}
[dir] .fr-desktop .fr-modal-wrapper .fr-modal-head i:hover {
  background: #ebebeb;
}
.fr-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  z-index: 2147483639;
}
[dir] .fr-overlay {
  background: #000;
}
[dir="ltr"] .fr-overlay,
[dir="rtl"] .fr-overlay {
  left: 0;
  right: 0;
}
ol.decimal_type {
  counter-reset: item;
}
ol.decimal_type > li {
  display: block;
}
ol.decimal_type > li:before {
  content: counters(item, ".") ". ";
  counter-increment: item;
}
.fr-popup {
  position: absolute;
  display: none;
  color: #222;
  -webkit-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  font-family: Arial, Helvetica, sans-serif;
  box-sizing: border-box;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  z-index: 2147483635;
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  -moz-background-clip: padding;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.2;
}
[dir] .fr-popup {
  background: #fff;
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  margin-top: 10px;
  border-radius: 6px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-popup {
  text-align: left;
}
[dir="rtl"] .fr-popup {
  text-align: right;
}
.fr-popup .fr-icon {
  vertical-align: middle;
  font-size: 20px;
  line-height: 1;
  font-weight: 400;
  box-sizing: content-box;
}
[dir] .fr-popup .fr-icon {
  text-align: center;
  cursor: pointer;
  padding: 6px;
}
.fr-popup .fr-icon-container {
  max-height: 200px;
  overflow: auto;
  box-sizing: border-box;
}
[dir] .fr-popup .fr-icon-container {
  padding: 20px;
}
@supports not (-ms-high-contrast: none) {
  .fr-popup .fr-icon-container {
    grid-template-columns: repeat(auto-fill, minmax(36px, 36px));
    display: grid;
  }
}
@media (min-width: 768px) {
  .fr-popup .fr-icon-container {
    min-width: 276px;
  }
}
@media (-ms-high-contrast: none), screen and (-ms-high-contrast: active) {
  .fr-popup .fr-icon-container {
    display: inline-flex;
  }
}
@media (-ms-high-contrast: none) and (max-width: 768px),
  screen and (-ms-high-contrast: active) and (max-width: 768px) {
  .fr-popup .fr-icon-container {
    width: 236px;
  }
}
[dir] .fr-popup .fr-input-focus {
  background: #f5f5f5;
}
[dir] .fr-popup.fr-above {
  margin-top: -10px;
  border-top: 0;
}
.fr-popup.fr-active {
  display: block;
}
.fr-popup.fr-hidden {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-popup.fr-empty {
  display: none !important;
}
.fr-popup .fr-hs {
  display: block !important;
}
.fr-popup .fr-hs.fr-hidden {
  display: none !important;
}
.fr-popup .fr-input-line {
  position: relative;
}
[dir] .fr-popup .fr-input-line {
  padding: 15px 0;
}
.fr-popup .fr-input-line input[type="text"],
.fr-popup .fr-input-line textarea {
  width: 100%;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  color: #222;
  font-size: 14px;
  position: relative;
  z-index: 2;
  box-sizing: border-box;
  -moz-transition: border 0.5s, padding 0.5s;
  -ms-transition: border 0.5s, padding 0.5s;
  -o-transition: border 0.5s, padding 0.5s;
}
[dir] .fr-popup .fr-input-line input[type="text"],
[dir] .fr-popup .fr-input-line textarea {
  margin-bottom: 1px;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #999;
  background: #fff;
  padding: 4px 12px;
  -webkit-transition: border 0.5s, padding 0.5s;
}
[dir] .fr-popup .fr-input-line input[type="text"]:hover,
[dir] .fr-popup .fr-input-line textarea:hover {
  border: 1px solid #515151;
}
[dir] .fr-popup .fr-input-line input[type="text"]:focus,
[dir] .fr-popup .fr-input-line textarea:focus {
  border: 2px solid #0098f7;
  padding: 3px 11px;
}
.fr-popup .fr-input-line input[type="text"] {
  height: 46px;
}
.fr-popup .fr-input-line input + label,
.fr-popup .fr-input-line textarea + label {
  position: absolute;
  top: 29px;
  font-size: 14px;
  color: grey;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  z-index: 3;
  display: block;
}
[dir] .fr-popup .fr-input-line input + label,
[dir] .fr-popup .fr-input-line textarea + label {
  -webkit-transition: all 0.5s ease;
  background: #fff;
  padding: 0;
  cursor: text;
}
[dir="ltr"] .fr-popup .fr-input-line input + label,
[dir="ltr"] .fr-popup .fr-input-line textarea + label {
  left: 12px;
}
[dir="rtl"] .fr-popup .fr-input-line input + label,
[dir="rtl"] .fr-popup .fr-input-line textarea + label {
  right: 12px;
}
.fr-popup .fr-input-line input.fr-not-empty + label,
.fr-popup .fr-input-line textarea.fr-not-empty + label {
  color: grey;
  width: auto;
  font-size: 11px;
  top: 9px;
}
[dir] .fr-popup .fr-input-line input.fr-not-empty + label,
[dir] .fr-popup .fr-input-line textarea.fr-not-empty + label {
  padding: 0 4px;
}
[dir="ltr"] .fr-popup .fr-input-line input.fr-not-empty + label,
[dir="ltr"] .fr-popup .fr-input-line textarea.fr-not-empty + label {
  left: 4px;
}
[dir="rtl"] .fr-popup .fr-input-line input.fr-not-empty + label,
[dir="rtl"] .fr-popup .fr-input-line textarea.fr-not-empty + label {
  right: 4px;
}
.fr-popup input,
.fr-popup textarea {
  user-select: text;
  -o-user-select: text;
  -moz-user-select: text;
  -khtml-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
  outline: none;
}
[dir] .fr-popup input,
[dir] .fr-popup textarea {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-popup textarea {
  resize: none;
}
.fr-popup .fr-buttons {
  white-space: nowrap;
  line-height: 0;
}
.fr-popup .fr-buttons .fr-btn {
  display: inline-block;
}
[dir] .fr-popup .fr-buttons .fr-btn {
  float: none;
}
[dir="ltr"] .fr-popup .fr-buttons .fr-btn,
[dir="rtl"] .fr-popup .fr-buttons .fr-btn {
  margin-left: 4px;
  margin-right: 4px;
}
[dir="ltr"] .fr-popup .fr-buttons .fr-btn i {
  float: left;
}
[dir="rtl"] .fr-popup .fr-buttons .fr-btn i {
  float: right;
}
.fr-popup .fr-buttons .fr-separator {
  display: inline-block;
}
[dir] .fr-popup .fr-buttons .fr-separator {
  float: none;
}
.fr-popup .fr-buttons.fr-tabs {
  -moz-border-radius: 6px 6px 0 0;
  -webkit-border-radius: 6px 6px 0 0;
  -moz-background-clip: padding;
  overflow: hidden;
}
[dir] .fr-popup .fr-buttons.fr-tabs {
  border-radius: 6px 6px 0 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background-color: #f5f5f5;
}
@media (max-width: 768px) {
  .fr-popup .fr-buttons.fr-tabs.fr-tabs-scroll {
    overflow: scroll;
    overflow-y: hidden;
    width: 276px;
  }
}
[dir] .fr-popup .fr-buttons:not(.fr-tabs) {
  padding: 5px;
}
.fr-popup .fr-layer {
  -moz-border-radius: 6px;
  -webkit-border-radius: 6px;
  -moz-background-clip: padding;
  width: 195px;
  box-sizing: border-box;
  display: none;
}
[dir] .fr-popup .fr-layer {
  border-radius: 6px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  margin: 20px;
}
@media (min-width: 768px) {
  .fr-popup .fr-layer {
    width: 260px;
  }
}
.fr-popup .fr-layer.fr-active {
  display: inline-block;
}
.fr-popup .fr-action-buttons {
  z-index: 7;
  height: 36px;
}
[dir="ltr"] .fr-popup .fr-action-buttons {
  text-align: right;
}
[dir="rtl"] .fr-popup .fr-action-buttons {
  text-align: left;
}
.fr-popup .fr-action-buttons button.fr-command {
  height: 36px;
  line-height: 1;
  color: #0098f7;
  text-decoration: none;
  font-size: 16px;
  outline: none;
  -moz-transition: background 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
}
[dir] .fr-popup .fr-action-buttons button.fr-command {
  padding: 10px;
  cursor: pointer;
  border: none;
  background: none;
  -webkit-transition: background 0.2s ease 0s;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-popup .fr-action-buttons button.fr-command + button {
  margin-left: 24px;
}
[dir="rtl"] .fr-popup .fr-action-buttons button.fr-command + button {
  margin-right: 24px;
}
.fr-popup .fr-action-buttons button.fr-command:focus,
.fr-popup .fr-action-buttons button.fr-command:hover {
  color: #0098f7;
}
[dir] .fr-popup .fr-action-buttons button.fr-command:focus,
[dir] .fr-popup .fr-action-buttons button.fr-command:hover {
  background: #ebebeb;
}
.fr-popup .fr-action-buttons button.fr-command:active {
  color: #0098f7;
}
[dir] .fr-popup .fr-action-buttons button.fr-command:active {
  background: #d6d6d6;
}
[dir] .fr-popup .fr-action-buttons button::-moz-focus-inner {
  border: 0;
}
.fr-popup .fr-checkbox {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  line-height: 1;
  box-sizing: content-box;
  vertical-align: middle;
}
[dir] .fr-popup .fr-checkbox {
  padding: 10px;
  border-radius: 100%;
}
.fr-popup .fr-checkbox svg {
  display: none;
  width: 10px;
  height: 10px;
}
[dir] .fr-popup .fr-checkbox svg {
  margin-top: 2px;
}
[dir="ltr"] .fr-popup .fr-checkbox svg {
  margin-left: 2px;
}
[dir="rtl"] .fr-popup .fr-checkbox svg {
  margin-right: 2px;
}
.fr-popup .fr-checkbox span {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  width: 18px;
  height: 18px;
  display: inline-block;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  -moz-transition: background 0.2s ease 0s, border-color 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s, border-color 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s, border-color 0.2s ease 0s;
}
[dir] .fr-popup .fr-checkbox span {
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -webkit-transition: background 0.2s ease 0s, border-color 0.2s ease 0s;
}
.fr-popup .fr-checkbox input {
  position: absolute;
  z-index: 2;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  height: 18px;
  width: 18px;
  top: 7px;
}
[dir] .fr-popup .fr-checkbox input {
  border: 0;
  cursor: pointer;
  margin: 0;
  padding: 0;
}
[dir="ltr"] .fr-popup .fr-checkbox input {
  left: 7px;
}
[dir="rtl"] .fr-popup .fr-checkbox input {
  right: 7px;
}
[dir] .fr-popup .fr-checkbox input:not(:checked) + span {
  border: 2px solid #999;
}
[dir] .fr-popup .fr-checkbox input:not(:checked):active + span {
  background-color: #f5f5f5;
}
[dir] .fr-popup .fr-checkbox input:not(:checked):focus + span,
[dir] .fr-popup .fr-checkbox input:not(:checked):hover + span {
  border-color: #515151;
}
[dir] .fr-popup .fr-checkbox input:checked + span {
  background: #0098f7;
  border: 2px solid #0098f7;
}
.fr-popup .fr-checkbox input:checked + span svg {
  display: block;
}
[dir] .fr-popup .fr-checkbox input:checked:active + span {
  background-color: #ecf5ff;
}
.fr-popup .fr-checkbox input:checked:focus + span,
.fr-popup .fr-checkbox input:checked:hover + span {
  -webkit-opacity: 0.8;
  -moz-opacity: 0.8;
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-popup .fr-checkbox-line {
  font-size: 14px;
  line-height: 1.4px;
}
[dir] .fr-popup .fr-checkbox-line {
  margin-top: 10px;
}
.fr-popup .fr-checkbox-line label {
  vertical-align: middle;
}
[dir] .fr-popup .fr-checkbox-line label {
  cursor: pointer;
  margin: 0 5px;
}
[dir="ltr"] .fr-popup.fr-rtl {
  direction: rtl;
  text-align: right;
}
[dir="rtl"] .fr-popup.fr-rtl {
  direction: ltr;
  text-align: left;
}
[dir="ltr"] .fr-popup.fr-rtl .fr-action-buttons {
  text-align: left;
}
[dir="rtl"] .fr-popup.fr-rtl .fr-action-buttons {
  text-align: right;
}
[dir="ltr"] .fr-popup.fr-rtl .fr-input-line input + label,
[dir="ltr"] .fr-popup.fr-rtl .fr-input-line textarea + label {
  left: auto;
  right: 0;
}
[dir="rtl"] .fr-popup.fr-rtl .fr-input-line input + label,
[dir="rtl"] .fr-popup.fr-rtl .fr-input-line textarea + label {
  right: auto;
  left: 0;
}
[dir="ltr"] .fr-popup.fr-rtl .fr-buttons .fr-separator.fr-vs {
  float: right;
}
[dir="rtl"] .fr-popup.fr-rtl .fr-buttons .fr-separator.fr-vs {
  float: left;
}
.fr-text-edit-layer {
  width: 250px;
  box-sizing: border-box;
  display: block !important;
}
.fr-toolbar {
  color: #222;
  position: relative;
  font-family: Arial, Helvetica, sans-serif;
  box-sizing: border-box;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.2;
  -moz-transition: padding-bottom 0.5s;
  -ms-transition: padding-bottom 0.5s;
  -o-transition: padding-bottom 0.5s;
}
[dir] .fr-toolbar {
  background: #fff;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  padding-bottom: 0;
  -webkit-transition: padding-bottom 0.5s;
}
[dir="ltr"] .fr-toolbar {
  text-align: left;
}
[dir="rtl"] .fr-toolbar {
  text-align: right;
}
.fr-toolbar:after {
  display: block;
  content: "";
}
[dir] .fr-toolbar:after {
  clear: both;
}
.fr-toolbar:after {
  height: 0;
}
.fr-toolbar .fr-newline {
  display: block;
  content: "";
  height: 1px;
  -moz-transition: height 0.5s;
  -ms-transition: height 0.5s;
  -o-transition: height 0.5s;
}
[dir] .fr-toolbar .fr-newline {
  clear: both;
  background: #efefef;
  -webkit-transition: height 0.5s;
}
[dir="ltr"] .fr-toolbar .fr-newline,
[dir="rtl"] .fr-toolbar .fr-newline {
  margin-left: 9px;
  margin-right: 9px;
}
[dir] .fr-toolbar.fr-toolbar-open {
  padding-bottom: 48px;
}
.fr-toolbar.fr-toolbar-open .fr-newline {
  height: 0;
}
[dir="ltr"] .fr-toolbar .fr-float-right {
  float: right;
}
[dir="ltr"] .fr-toolbar .fr-float-left,
[dir="rtl"] .fr-toolbar .fr-float-right {
  float: left;
}
[dir="rtl"] .fr-toolbar .fr-float-left {
  float: right;
}
.fr-toolbar .fr-more-toolbar {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
  -moz-transition: height 0.5s;
  -ms-transition: height 0.5s;
  -o-transition: height 0.5s;
  height: 0;
  z-index: 2;
  overflow: hidden;
  position: absolute;
  box-sizing: border-box;
  width: 100%;
}
[dir] .fr-toolbar .fr-more-toolbar {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background-color: #f5f5f5;
  -webkit-transition: height 0.5s;
}
[dir="ltr"] .fr-toolbar .fr-more-toolbar {
  float: left;
}
[dir="rtl"] .fr-toolbar .fr-more-toolbar {
  float: right;
}
.fr-toolbar .fr-more-toolbar.fr-expanded {
  height: 48px;
}
.fr-toolbar .fr-more-toolbar.fr-overflow-visible {
  overflow: visible;
}
[dir="ltr"] .fr-toolbar .fr-more-toolbar > .fr-command.fr-btn,
[dir="rtl"] .fr-toolbar .fr-more-toolbar > .fr-command.fr-btn {
  margin-left: 4px;
  margin-right: 4px;
}
.fr-toolbar .fr-btn-grp {
  display: inline-block;
}
[dir="ltr"] .fr-toolbar .fr-btn-grp {
  margin: 0 17px 0 12px;
}
[dir="rtl"] .fr-toolbar .fr-btn-grp {
  margin: 0 12px 0 17px;
}
@media (max-width: 768px) {
  [dir="ltr"] .fr-toolbar .fr-btn-grp {
    margin: 0 7px 0 6px;
  }
  [dir="rtl"] .fr-toolbar .fr-btn-grp {
    margin: 0 6px 0 7px;
  }
}
.fr-toolbar .fr-command.fr-btn.fr-open {
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  -moz-background-clip: padding;
}
[dir] .fr-toolbar .fr-command.fr-btn.fr-open {
  margin-top: 10px;
  margin-bottom: -1px;
  border-radius: 4px 4px 0 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir]
  .fr-toolbar
  .fr-command.fr-btn.fr-open:not(:hover):not(:focus):not(:active) {
  background: #f5f5f5;
}
[dir="ltr"] .fr-toolbar.fr-rtl {
  text-align: right;
}
[dir="rtl"] .fr-toolbar.fr-rtl {
  text-align: left;
}
.fr-toolbar.fr-inline {
  display: none;
  -webkit-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  -moz-box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  white-space: nowrap;
  position: absolute;
  z-index: 2147483630;
}
[dir] .fr-toolbar.fr-inline {
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2),
    0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
  border: 0;
  margin-top: 5px;
}
[dir] .fr-toolbar.fr-inline.fr-above {
  margin-top: -10px;
  border-top: 0;
}
.fr-toolbar.fr-inline .fr-newline {
  height: 0;
}
.fr-toolbar.fr-top {
  top: 0;
  -moz-border-radius: 10px 10px 0 0;
  -webkit-border-radius: 10px 10px 0 0;
  -moz-background-clip: padding;
}
[dir] .fr-toolbar.fr-top {
  border-bottom: 0;
  border-radius: 10px 10px 0 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-toolbar.fr-bottom {
  bottom: 0;
  -moz-border-radius: 0 0 10px 10px;
  -webkit-border-radius: 0 0 10px 10px;
  -moz-background-clip: padding;
}
[dir] .fr-toolbar.fr-bottom {
  border-top: 0;
  padding-bottom: 0;
  border-radius: 0 0 10px 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-toolbar.fr-bottom .fr-newline {
  -moz-transition: padding-top 0.5s;
  -ms-transition: padding-top 0.5s;
  -o-transition: padding-top 0.5s;
}
[dir] .fr-toolbar.fr-bottom .fr-newline {
  padding-top: 0;
  -webkit-transition: padding-top 0.5s;
}
[dir] .fr-toolbar.fr-bottom.fr-toolbar-open .fr-newline {
  padding-top: 48px;
  background: #fff;
}
.fr-toolbar.fr-bottom .fr-command.fr-btn.fr-open {
  -moz-border-radius: 0 0 4px 4px;
  -webkit-border-radius: 0 0 4px 4px;
  -moz-background-clip: padding;
}
[dir] .fr-toolbar.fr-bottom .fr-command.fr-btn.fr-open {
  margin-top: -1px;
  margin-bottom: 10px;
  border-radius: 0 0 4px 4px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-toolbar.fr-sticky-on {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-toolbar.fr-sticky-on {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-separator {
  display: block;
  vertical-align: top;
}
[dir] .fr-separator {
  background: #ebebeb;
}
[dir="ltr"] .fr-separator {
  float: left;
}
[dir="rtl"] .fr-separator {
  float: right;
}
.fr-separator + .fr-separator {
  display: none;
}
.fr-separator.fr-vs {
  height: 32px;
  width: 1px;
}
[dir] .fr-separator.fr-vs {
  margin: 8px 4px;
}
.fr-separator.fr-hs {
  width: calc(100% - 8px);
  height: 1px;
}
[dir] .fr-separator.fr-hs {
  clear: both;
  margin: 0 4px;
}
.fr-separator.fr-hidden {
  display: none !important;
}
[dir="ltr"] .fr-rtl .fr-separator {
  float: right;
}
[dir="rtl"] .fr-rtl .fr-separator {
  float: left;
}
[dir] .fr-toolbar.fr-inline .fr-separator.fr-hs {
  float: none;
}
.fr-toolbar.fr-inline .fr-separator.fr-vs {
  display: inline-block;
}
[dir] .fr-toolbar.fr-inline .fr-separator.fr-vs {
  float: none;
}
.second-toolbar {
  line-height: 1.4;
  -moz-border-radius: 0 0 10px 10px;
  -webkit-border-radius: 0 0 10px 10px;
  -moz-background-clip: padding;
}
[dir] .second-toolbar {
  border: 1px solid #ccc;
  border-top: 0;
  background: #fff;
  border-radius: 0 0 10px 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.second-toolbar:after {
  display: block;
  content: "";
  height: 0;
}
[dir] .second-toolbar:after {
  clear: both;
}
#logo {
  outline: none;
}
[dir="ltr"] #logo {
  float: left;
}
[dir="rtl"] #logo {
  float: right;
}
#logo > span {
  display: inline-block;
  font-family: sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: #b1b2b7;
  -moz-transition: color 0.3s;
  -ms-transition: color 0.3s;
  -o-transition: color 0.3s;
}
[dir] #logo > span {
  -webkit-transition: color 0.3s;
}
[dir="ltr"] #logo > span {
  float: left;
  padding: 11px 5px 10px 15px;
}
[dir="rtl"] #logo > span {
  float: right;
  padding: 11px 15px 10px 5px;
}
#logo > svg {
  height: 20px;
  width: 47px;
}
[dir] #logo > svg {
  margin: 7px 0;
  cursor: pointer;
}
#logo > svg * {
  -moz-transition: fill 0.3s;
  -ms-transition: fill 0.3s;
  -o-transition: fill 0.3s;
}
[dir] #logo > svg * {
  -webkit-transition: fill 0.3s;
}
#logo:focus > span,
#logo:hover > span {
  color: #0098f7;
}
#logo:focus > svg .fr-logo,
#logo:hover > svg .fr-logo {
  fill: #0098f7;
}
.fr-visibility-helper {
  display: none;
}
[dir="ltr"] .fr-visibility-helper {
  margin-left: 0 !important;
}
[dir="rtl"] .fr-visibility-helper {
  margin-right: 0 !important;
}
@media (min-width: 768px) {
  [dir="ltr"] .fr-visibility-helper {
    margin-left: 1px !important;
  }
  [dir="rtl"] .fr-visibility-helper {
    margin-right: 1px !important;
  }
}
@media (min-width: 992px) {
  [dir="ltr"] .fr-visibility-helper {
    margin-left: 2px !important;
  }
  [dir="rtl"] .fr-visibility-helper {
    margin-right: 2px !important;
  }
}
@media (min-width: 1200px) {
  [dir="ltr"] .fr-visibility-helper {
    margin-left: 3px !important;
  }
  [dir="rtl"] .fr-visibility-helper {
    margin-right: 3px !important;
  }
}
.fr-opacity-0 {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-box {
  position: relative;
}
.fr-sticky {
  position: -moz-sticky;
  position: -ms-sticky;
  position: -o-sticky;
  position: sticky;
}
.fr-sticky-off {
  position: relative;
}
.fr-sticky-on {
  position: fixed;
  z-index: 10;
}
.fr-sticky-on.fr-sticky-ios {
  position: absolute;
  width: auto !important;
}
[dir="ltr"] .fr-sticky-on.fr-sticky-ios,
[dir="rtl"] .fr-sticky-on.fr-sticky-ios {
  left: 0;
  right: 0;
}
.fr-sticky-dummy {
  display: none;
}
.fr-sticky-box > .fr-sticky-dummy,
.fr-sticky-on + .fr-sticky-dummy {
  display: block;
}
span.fr-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
[dir] span.fr-sr-only {
  padding: 0;
  margin: -1px;
  border: 0;
}
.fr-box .fr-counter {
  color: #999;
  content: attr(data-chars);
  font-size: 14px;
  font-family: sans-serif;
  z-index: 1;
  -moz-background-clip: padding;
}
[dir] .fr-box .fr-counter {
  padding: 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-box .fr-counter {
  float: right;
  border-radius: 2px 0 0 0;
  -moz-border-radius: 2px 0 0 0;
  -webkit-border-radius: 2px 0 0 0;
}
[dir="rtl"] .fr-box .fr-counter {
  float: left;
  border-radius: 0 2px 0 0;
  -moz-border-radius: 0 2px 0 0;
  -webkit-border-radius: 0 2px 0 0;
}
.fr-box.fr-rtl .fr-counter {
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-rtl .fr-counter {
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-box.fr-rtl .fr-counter {
  left: 0;
  right: auto;
  border-left: none;
  border-radius: 0 2px 0 0;
  -moz-border-radius: 0 2px 0 0;
  -webkit-border-radius: 0 2px 0 0;
}
[dir="rtl"] .fr-box.fr-rtl .fr-counter {
  right: 0;
  left: auto;
  border-right: none;
  border-radius: 2px 0 0 0;
  -moz-border-radius: 2px 0 0 0;
  -webkit-border-radius: 2px 0 0 0;
}
.fr-box.fr-code-view .fr-counter {
  display: none;
}
textarea.fr-code {
  display: none;
  width: 100%;
  resize: none;
  -moz-resize: none;
  -webkit-resize: none;
  box-sizing: border-box;
  font-family: Courier New, monospace;
  font-size: 14px;
  color: #000;
  outline: none;
}
[dir] textarea.fr-code {
  border: none;
  padding: 10px;
  margin: 0;
  background: #fff;
}
[dir="ltr"] .fr-box.fr-rtl textarea.fr-code {
  direction: rtl;
}
[dir="rtl"] .fr-box.fr-rtl textarea.fr-code {
  direction: ltr;
}
.fr-box .CodeMirror {
  display: none;
}
.fr-box.fr-code-view textarea.fr-code {
  display: block;
}
.fr-box.fr-code-view .fr-element,
.fr-box.fr-code-view .fr-iframe,
.fr-box.fr-code-view .fr-placeholder {
  display: none;
}
.fr-box.fr-code-view .CodeMirror,
.fr-box.fr-inline.fr-code-view .fr-command.fr-btn.html-switch {
  display: block;
}
.fr-box.fr-inline .fr-command.fr-btn.html-switch {
  position: absolute;
  top: 0;
  display: none;
  color: #333;
  -moz-outline: 0;
  outline: 0;
  line-height: 1;
  -moz-transition: background 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
  z-index: 2;
  box-sizing: border-box;
  text-decoration: none;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
[dir] .fr-box.fr-inline .fr-command.fr-btn.html-switch {
  background: #fff;
  border: 0;
  cursor: pointer;
  padding: 8px 7px;
  -webkit-transition: background 0.2s ease 0s;
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-box.fr-inline .fr-command.fr-btn.html-switch {
  right: 0;
  text-align: left;
}
[dir="rtl"] .fr-box.fr-inline .fr-command.fr-btn.html-switch {
  left: 0;
  text-align: right;
}
.fr-box.fr-inline .fr-command.fr-btn.html-switch i {
  font-size: 24px;
  width: 24px;
}
[dir] .fr-box.fr-inline .fr-command.fr-btn.html-switch i {
  text-align: center;
}
[dir] .fr-box.fr-inline .fr-command.fr-btn.html-switch.fr-desktop:hover {
  background: #ebebeb;
}
[dir] .fr-popup .fr-layer.fr-color-hex-layer {
  margin: 0;
  padding: 0 20px 20px;
}
[dir="ltr"] .fr-popup .fr-layer.fr-color-hex-layer {
  float: left;
}
[dir="rtl"] .fr-popup .fr-layer.fr-color-hex-layer {
  float: right;
}
.fr-popup .fr-layer.fr-color-hex-layer .fr-input-line {
  width: calc(100% - 50px);
}
[dir] .fr-popup .fr-layer.fr-color-hex-layer .fr-input-line {
  padding: 15px 0 0;
}
[dir="ltr"] .fr-popup .fr-layer.fr-color-hex-layer .fr-input-line {
  float: left;
}
[dir="rtl"] .fr-popup .fr-layer.fr-color-hex-layer .fr-input-line {
  float: right;
}
.fr-popup .fr-layer.fr-color-hex-layer .fr-action-buttons {
  width: 38px;
  height: 40px;
}
[dir] .fr-popup .fr-layer.fr-color-hex-layer .fr-action-buttons {
  padding: 17px 0 0;
  margin: 0;
}
[dir="ltr"] .fr-popup .fr-layer.fr-color-hex-layer .fr-action-buttons {
  float: right;
}
[dir="rtl"] .fr-popup .fr-layer.fr-color-hex-layer .fr-action-buttons {
  float: left;
}
.fr-popup .fr-layer.fr-color-hex-layer .fr-action-buttons button.fr-command {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  font-size: 13px;
  height: 40px;
  width: 38px;
}
[dir]
  .fr-popup
  .fr-layer.fr-color-hex-layer
  .fr-action-buttons
  button.fr-command {
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-popup .fr-separator + .fr-colors-tabs,
[dir="rtl"] .fr-popup .fr-separator + .fr-colors-tabs {
  margin-left: 2px;
  margin-right: 2px;
}
.fr-popup .fr-color-set {
  line-height: 0;
  display: none;
}
.fr-popup .fr-color-set.fr-selected-set {
  display: block;
}
[dir] .fr-popup .fr-color-set.fr-selected-set {
  padding: 20px 20px 0;
}
.fr-popup .fr-color-set > span {
  display: inline-block;
  width: 32px;
  height: 32px;
  position: relative;
  z-index: 1;
}
.fr-popup .fr-color-set > span > i,
.fr-popup .fr-color-set > span > svg {
  line-height: 32px;
  height: 24px;
  width: 24px;
  font-size: 13px;
  position: absolute;
  bottom: 0;
}
[dir] .fr-popup .fr-color-set > span > i,
[dir] .fr-popup .fr-color-set > span > svg {
  text-align: center;
  margin: 4px;
  cursor: default;
}
[dir="ltr"] .fr-popup .fr-color-set > span > i,
[dir="ltr"] .fr-popup .fr-color-set > span > svg {
  left: 0;
}
[dir="rtl"] .fr-popup .fr-color-set > span > i,
[dir="rtl"] .fr-popup .fr-color-set > span > svg {
  right: 0;
}
.fr-popup .fr-color-set > span > i path,
.fr-popup .fr-color-set > span > svg path {
  fill: #222;
}
.fr-popup .fr-color-set > span .fr-selected-color {
  color: #fff;
  font-family: FontAwesome;
  font-size: 13px;
  font-weight: 400;
  line-height: 32px;
  position: absolute;
  top: 0;
  bottom: 0;
}
[dir] .fr-popup .fr-color-set > span .fr-selected-color {
  text-align: center;
  cursor: default;
}
[dir="ltr"] .fr-popup .fr-color-set > span .fr-selected-color,
[dir="rtl"] .fr-popup .fr-color-set > span .fr-selected-color {
  right: 0;
  left: 0;
}
.fr-popup .fr-color-set > span:focus,
.fr-popup .fr-color-set > span:hover {
  outline: 1px solid #222;
  z-index: 2;
}
[dir="ltr"]
  .fr-rtl
  .fr-popup
  .fr-colors-tabs
  .fr-colors-tab.fr-selected-tab[data-param1="text"]
  ~ [data-param1="background"]:after {
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  -ms-transform: translate3d(100%, 0, 0);
  -o-transform: translate3d(100%, 0, 0);
}
[dir="rtl"]
  .fr-rtl
  .fr-popup
  .fr-colors-tabs
  .fr-colors-tab.fr-selected-tab[data-param1="text"]
  ~ [data-param1="background"]:after {
  -webkit-transform: translate3d(-100%, 0, 0);
  -moz-transform: translate3d(-100%, 0, 0);
  -ms-transform: translate3d(-100%, 0, 0);
  -o-transform: translate3d(-100%, 0, 0);
}
.fr-drag-helper {
  height: 2px;
  -webkit-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  position: absolute;
  z-index: 2147483640;
  display: none;
}
[dir] .fr-drag-helper {
  background: #0098f7;
  margin-top: -1px;
}
.fr-drag-helper.fr-visible {
  display: block;
}
.fr-dragging {
  -webkit-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-popup .fr-emoticon {
  width: 24px;
  height: 24px;
  font-family: Apple Color Emoji, Segoe UI Emoji, NotoColorEmoji,
    Segoe UI Symbol, Android Emoji, EmojiSymbols;
}
.fr-popup .fr-emoticon img {
  height: 24px;
  width: 24px;
}
[dir="ltr"] .fr-popup .fr-command.fr-btn.fr-tabs-unicode {
  padding: 0 0 0 14px;
}
[dir="rtl"] .fr-popup .fr-command.fr-btn.fr-tabs-unicode {
  padding: 0 14px 0 0;
}
@media (-ms-high-contrast: none) and (min-width: 768px),
  screen and (-ms-high-contrast: active) and (min-width: 768px) {
  .fr-popup .fr-icon-container.fr-emoticon-container {
    width: 368px;
  }
}
.fr-popup .fr-file-upload-layer {
  position: relative;
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 140%;
  box-sizing: border-box;
}
[dir] .fr-popup .fr-file-upload-layer {
  border: 2px dashed #bdbdbd;
  padding: 25px 0;
  margin: 20px;
  text-align: center;
}
[dir] .fr-popup .fr-file-upload-layer:hover {
  background: #ebebeb;
}
[dir] .fr-popup .fr-file-upload-layer.fr-drop {
  background: #ebebeb;
  border-color: #0098f7;
}
.fr-popup .fr-file-upload-layer .fr-form {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 2147483640;
  overflow: hidden;
  width: 100% !important;
}
[dir] .fr-popup .fr-file-upload-layer .fr-form {
  margin: 0 !important;
  padding: 0 !important;
}
[dir="ltr"] .fr-popup .fr-file-upload-layer .fr-form,
[dir="rtl"] .fr-popup .fr-file-upload-layer .fr-form {
  left: 0;
  right: 0;
}
.fr-popup .fr-file-upload-layer .fr-form input {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 500%;
  height: 100%;
  font-size: 400px;
}
[dir] .fr-popup .fr-file-upload-layer .fr-form input {
  cursor: pointer;
  margin: 0;
}
[dir="ltr"] .fr-popup .fr-file-upload-layer .fr-form input {
  right: 0;
}
[dir="rtl"] .fr-popup .fr-file-upload-layer .fr-form input {
  left: 0;
}
.fr-popup .fr-file-progress-bar-layer {
  box-sizing: border-box;
}
.fr-popup .fr-file-progress-bar-layer > h3 {
  font-size: 16px;
  font-weight: 400;
}
[dir] .fr-popup .fr-file-progress-bar-layer > h3 {
  margin: 10px 0;
}
.fr-popup .fr-file-progress-bar-layer > div.fr-action-buttons {
  display: none;
}
.fr-popup .fr-file-progress-bar-layer > div.fr-loader {
  height: 10px;
  width: 100%;
  overflow: hidden;
  position: relative;
}
[dir] .fr-popup .fr-file-progress-bar-layer > div.fr-loader {
  background: #b3e0fd;
  margin-top: 20px;
}
.fr-popup .fr-file-progress-bar-layer > div.fr-loader span {
  display: block;
  height: 100%;
  width: 0;
  -moz-transition: width 0.2s ease 0s;
  -ms-transition: width 0.2s ease 0s;
  -o-transition: width 0.2s ease 0s;
}
[dir] .fr-popup .fr-file-progress-bar-layer > div.fr-loader span {
  background: #0098f7;
  -webkit-transition: width 0.2s ease 0s;
}
.fr-popup .fr-file-progress-bar-layer > div.fr-loader.fr-indeterminate span {
  width: 30% !important;
  position: absolute;
  top: 0;
}
[dir="ltr"]
  .fr-popup
  .fr-file-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-ltr 2s linear infinite;
  animation: loading-ltr 2s linear infinite;
}
[dir="rtl"]
  .fr-popup
  .fr-file-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-rtl 2s linear infinite;
  animation: loading-rtl 2s linear infinite;
}
.fr-popup .fr-file-progress-bar-layer.fr-error > div.fr-loader {
  display: none;
}
.fr-popup .fr-file-progress-bar-layer.fr-error > div.fr-action-buttons {
  display: block;
}
body.fr-fullscreen {
  overflow: hidden;
  height: 100%;
  width: 100%;
  position: fixed;
}
.fr-box.fr-fullscreen {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 2147483630 !important;
  width: auto !important;
}
[dir] .fr-box.fr-fullscreen {
  margin: 0 !important;
}
[dir="ltr"] .fr-box.fr-fullscreen,
[dir="rtl"] .fr-box.fr-fullscreen {
  left: 0;
  right: 0;
}
.fr-box.fr-fullscreen.fr-basic.fr-top .fr-wrapper {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-fullscreen.fr-basic.fr-top .fr-wrapper {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-box.fr-fullscreen.fr-basic.fr-bottom .fr-wrapper {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-fullscreen.fr-basic.fr-bottom .fr-wrapper {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-box.fr-fullscreen .fr-toolbar {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-fullscreen .fr-toolbar {
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-box.fr-fullscreen .fr-toolbar.fr-top {
  top: 0 !important;
}
.fr-box.fr-fullscreen .fr-toolbar.fr-bottom {
  bottom: 0 !important;
}
.fr-box.fr-fullscreen .second-toolbar {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -moz-background-clip: padding;
}
[dir] .fr-box.fr-fullscreen .second-toolbar {
  margin-top: 0;
  border-radius: 0;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-fullscreen-wrapper {
  z-index: 2147483640 !important;
  width: 100% !important;
  overflow: visible !important;
}
[dir] .fr-fullscreen-wrapper {
  margin: 0 !important;
  padding: 0 !important;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal {
  padding: 20px 20px 10px;
}
[dir="ltr"] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal {
  text-align: left;
}
[dir="rtl"] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal {
  text-align: right;
}
.fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table {
  border-collapse: collapse;
  font-size: 14px;
  line-height: 1.5;
  width: 100%;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table + table {
  margin-top: 20px;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table tr {
  border: 0;
}
[dir="ltr"] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table th {
  text-align: left;
}
[dir="rtl"] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table th {
  text-align: right;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table td,
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table th {
  padding: 6px 0 4px;
}
[dir] .fr-modal .fr-modal-wrapper .fr-modal-body .fr-help-modal table tbody tr {
  border-bottom: 1px solid #ebebeb;
}
.fr-modal
  .fr-modal-wrapper
  .fr-modal-body
  .fr-help-modal
  table
  tbody
  td:first-child {
  width: 60%;
  color: #646464;
}
.fr-modal
  .fr-modal-wrapper
  .fr-modal-body
  .fr-help-modal
  table
  tbody
  td:nth-child(n + 2) {
  letter-spacing: 0.5px;
}
[dir] .fr-element img {
  cursor: pointer;
  padding: 0 1px;
}
.fr-image-resizer {
  position: absolute;
  display: none;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  box-sizing: content-box;
}
[dir] .fr-image-resizer {
  border: 1px solid #0098f7;
}
.fr-image-resizer.fr-active {
  display: block;
}
.fr-image-resizer .fr-handler {
  display: block;
  position: absolute;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  z-index: 4;
  box-sizing: border-box;
}
[dir] .fr-image-resizer .fr-handler {
  background: #0098f7;
  border: 1px solid #fff;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hnw {
  cursor: nw-resize;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hne,
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hnw {
  cursor: ne-resize;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hne {
  cursor: nw-resize;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hsw {
  cursor: sw-resize;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hse,
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hsw {
  cursor: se-resize;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hse {
  cursor: sw-resize;
}
.fr-image-resizer .fr-handler {
  width: 12px;
  height: 12px;
}
.fr-image-resizer .fr-handler.fr-hnw {
  top: -6px;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hnw {
  left: -6px;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hnw {
  right: -6px;
}
.fr-image-resizer .fr-handler.fr-hne {
  top: -6px;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hne {
  right: -6px;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hne {
  left: -6px;
}
.fr-image-resizer .fr-handler.fr-hsw {
  bottom: -6px;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hsw {
  left: -6px;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hsw {
  right: -6px;
}
.fr-image-resizer .fr-handler.fr-hse {
  bottom: -6px;
}
[dir="ltr"] .fr-image-resizer .fr-handler.fr-hse {
  right: -6px;
}
[dir="rtl"] .fr-image-resizer .fr-handler.fr-hse {
  left: -6px;
}
@media (min-width: 1200px) {
  .fr-image-resizer .fr-handler {
    width: 10px;
    height: 10px;
  }
  .fr-image-resizer .fr-handler.fr-hnw {
    top: -5px;
  }
  [dir="ltr"] .fr-image-resizer .fr-handler.fr-hnw {
    left: -5px;
  }
  [dir="rtl"] .fr-image-resizer .fr-handler.fr-hnw {
    right: -5px;
  }
  .fr-image-resizer .fr-handler.fr-hne {
    top: -5px;
  }
  [dir="ltr"] .fr-image-resizer .fr-handler.fr-hne {
    right: -5px;
  }
  [dir="rtl"] .fr-image-resizer .fr-handler.fr-hne {
    left: -5px;
  }
  .fr-image-resizer .fr-handler.fr-hsw {
    bottom: -5px;
  }
  [dir="ltr"] .fr-image-resizer .fr-handler.fr-hsw {
    left: -5px;
  }
  [dir="rtl"] .fr-image-resizer .fr-handler.fr-hsw {
    right: -5px;
  }
  .fr-image-resizer .fr-handler.fr-hse {
    bottom: -5px;
  }
  [dir="ltr"] .fr-image-resizer .fr-handler.fr-hse {
    right: -5px;
  }
  [dir="rtl"] .fr-image-resizer .fr-handler.fr-hse {
    left: -5px;
  }
}
.fr-image-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 2147483640;
  display: none;
}
[dir="ltr"] .fr-image-overlay,
[dir="rtl"] .fr-image-overlay {
  left: 0;
  right: 0;
}
.fr-popup .fr-image-upload-layer {
  position: relative;
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 140%;
}
[dir] .fr-popup .fr-image-upload-layer {
  border: 2px dashed #bdbdbd;
  padding: 25px 0;
  margin: 20px;
  text-align: center;
}
[dir] .fr-popup .fr-image-upload-layer:hover {
  background: #ebebeb;
}
[dir] .fr-popup .fr-image-upload-layer.fr-drop {
  background: #ebebeb;
  border-color: #0098f7;
}
.fr-popup .fr-image-upload-layer .fr-form {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 2147483640;
  overflow: hidden;
  width: 100% !important;
}
[dir] .fr-popup .fr-image-upload-layer .fr-form {
  margin: 0 !important;
  padding: 0 !important;
}
[dir="ltr"] .fr-popup .fr-image-upload-layer .fr-form,
[dir="rtl"] .fr-popup .fr-image-upload-layer .fr-form {
  left: 0;
  right: 0;
}
.fr-popup .fr-image-upload-layer .fr-form input {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 500%;
  height: 100%;
  font-size: 400px;
}
[dir] .fr-popup .fr-image-upload-layer .fr-form input {
  cursor: pointer;
  margin: 0;
}
[dir="ltr"] .fr-popup .fr-image-upload-layer .fr-form input {
  right: 0;
}
[dir="rtl"] .fr-popup .fr-image-upload-layer .fr-form input {
  left: 0;
}
.fr-popup .fr-image-progress-bar-layer > h3 {
  font-size: 16px;
  font-weight: 400;
}
[dir] .fr-popup .fr-image-progress-bar-layer > h3 {
  margin: 10px 0;
}
.fr-popup .fr-image-progress-bar-layer > div.fr-action-buttons {
  display: none;
}
.fr-popup .fr-image-progress-bar-layer > div.fr-loader {
  height: 10px;
  width: 100%;
  overflow: hidden;
  position: relative;
}
[dir] .fr-popup .fr-image-progress-bar-layer > div.fr-loader {
  background: #b3e0fd;
  margin-top: 20px;
}
.fr-popup .fr-image-progress-bar-layer > div.fr-loader span {
  display: block;
  height: 100%;
  width: 0;
  -moz-transition: width 0.2s ease 0s;
  -ms-transition: width 0.2s ease 0s;
  -o-transition: width 0.2s ease 0s;
}
[dir] .fr-popup .fr-image-progress-bar-layer > div.fr-loader span {
  background: #0098f7;
  -webkit-transition: width 0.2s ease 0s;
}
.fr-popup .fr-image-progress-bar-layer > div.fr-loader.fr-indeterminate span {
  width: 30% !important;
  position: absolute;
  top: 0;
}
[dir="ltr"]
  .fr-popup
  .fr-image-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-ltr 2s linear infinite;
  animation: loading-ltr 2s linear infinite;
}
[dir="rtl"]
  .fr-popup
  .fr-image-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-rtl 2s linear infinite;
  animation: loading-rtl 2s linear infinite;
}
.fr-popup .fr-image-progress-bar-layer.fr-error > div.fr-loader {
  display: none;
}
.fr-popup .fr-image-progress-bar-layer.fr-error > div.fr-action-buttons {
  display: block;
}
.fr-image-size-layer .fr-image-group .fr-input-line {
  width: calc(50% - 5px);
  display: inline-block;
}
[dir="ltr"]
  .fr-image-size-layer
  .fr-image-group
  .fr-input-line
  + .fr-input-line {
  margin-left: 10px;
}
[dir="rtl"]
  .fr-image-size-layer
  .fr-image-group
  .fr-input-line
  + .fr-input-line {
  margin-right: 10px;
}
.fr-uploading {
  -webkit-opacity: 0.4;
  -moz-opacity: 0.4;
  opacity: 0.4;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
@-webkit-keyframes loading-ltr {
  0% {
    left: -25%;
  }
  to {
    left: 100%;
  }
}
@-webkit-keyframes loading-rtl {
  0% {
    left: -25%;
  }
  to {
    left: 100%;
  }
}
@keyframes loading-ltr {
  0% {
    left: -25%;
  }
  to {
    left: 100%;
  }
}
@keyframes loading-rtl {
  0% {
    left: -25%;
  }
  to {
    left: 100%;
  }
}
.fr-modal .fr-modal-head .fr-modal-head-line:after {
  display: block;
  content: "";
  height: 0;
}
[dir] .fr-modal .fr-modal-head .fr-modal-head-line:after {
  clear: both;
}
[dir] .fr-modal .fr-modal-head .fr-modal-head-line .fr-modal-more {
  margin-top: 10px;
}
.fr-modal .fr-modal-head .fr-modal-head-line .fr-modal-more.fr-not-available {
  opacity: 0;
  width: 0;
}
[dir]
  .fr-modal
  .fr-modal-head
  .fr-modal-head-line
  .fr-modal-more.fr-not-available {
  padding: 12px 0;
}
.fr-modal .fr-modal-head .fr-modal-tags {
  display: none;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags {
  padding: 0 20px;
}
[dir="ltr"] .fr-modal .fr-modal-head .fr-modal-tags {
  text-align: left;
}
[dir="rtl"] .fr-modal .fr-modal-head .fr-modal-tags {
  text-align: right;
}
.fr-modal .fr-modal-head .fr-modal-tags a {
  display: inline-block;
  opacity: 0;
  text-decoration: none;
  -moz-border-radius: 32px;
  -webkit-border-radius: 32px;
  -moz-background-clip: padding;
  -moz-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
  -ms-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
  -o-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a {
  padding: 6px 12px;
  border-radius: 32px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -webkit-transition: opacity 0.2s ease 0s, background 0.2s ease 0s;
  cursor: pointer;
  background-color: #f5f5f5;
}
[dir="ltr"] .fr-modal .fr-modal-head .fr-modal-tags a {
  margin: 8px 0 8px 8px;
}
[dir="rtl"] .fr-modal .fr-modal-head .fr-modal-tags a {
  margin: 8px 8px 8px 0;
}
.fr-modal .fr-modal-head .fr-modal-tags a:focus {
  outline: none;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a:focus,
[dir] .fr-modal .fr-modal-head .fr-modal-tags a:hover {
  background-color: #ebebeb;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a:active {
  background-color: #d6d6d6;
}
.fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag {
  color: #0098f7;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag {
  background-color: #ecf5ff;
}
.fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:focus {
  outline: none;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:focus,
[dir] .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:hover {
  background-color: #ebebeb;
}
[dir] .fr-modal .fr-modal-head .fr-modal-tags a.fr-selected-tag:active {
  background-color: #d6d6d6;
}
.fr-show-tags .fr-modal-more svg path {
  fill: #0098f7;
}
div.fr-modal-body {
  -moz-transition: background 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s;
}
[dir] div.fr-modal-body {
  -webkit-transition: background 0.2s ease 0s;
}
div.fr-modal-body .fr-preloader {
  display: block;
}
[dir] div.fr-modal-body .fr-preloader {
  margin: 50px auto;
}
[dir] div.fr-modal-body div.fr-image-list {
  text-align: center;
  margin: 0 20px;
  padding: 0;
}
div.fr-modal-body div.fr-image-list .fr-list-column {
  width: calc((100% - 20px) / 2);
}
[dir="ltr"] div.fr-modal-body div.fr-image-list .fr-list-column {
  float: left;
}
[dir="rtl"] div.fr-modal-body div.fr-image-list .fr-list-column {
  float: right;
}
@media (min-width: 768px) and (max-width: 1199px) {
  div.fr-modal-body div.fr-image-list .fr-list-column {
    width: calc((100% - 40px) / 3);
  }
}
@media (min-width: 1200px) {
  div.fr-modal-body div.fr-image-list .fr-list-column {
    width: calc((100% - 60px) / 4);
  }
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  .fr-list-column
  + .fr-list-column {
  margin-left: 20px;
}
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  .fr-list-column
  + .fr-list-column {
  margin-right: 20px;
}
div.fr-modal-body div.fr-image-list div.fr-image-container {
  position: relative;
  width: 100%;
  display: block;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  overflow: hidden;
}
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container {
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container + div,
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container:first-child {
  margin-top: 20px;
}
div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:after {
  position: absolute;
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -moz-transition: opacity 0.2s ease 0s;
  -ms-transition: opacity 0.2s ease 0s;
  -o-transition: opacity 0.2s ease 0s;
  content: "";
  top: 0;
  bottom: 0;
  z-index: 2;
}
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:after {
  -webkit-transition: opacity 0.2s ease 0s;
  background: #000;
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:after,
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:after {
  left: 0;
  right: 0;
}
div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:before {
  content: attr(data-deleting);
  color: #fff;
  top: 0;
  bottom: 0;
  position: absolute;
  z-index: 3;
  font-size: 15px;
  height: 20px;
}
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:before {
  margin: auto;
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:before,
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-image-deleting:before {
  left: 0;
  right: 0;
}
div.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty {
  height: 95px;
  z-index: 1;
}
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty {
  background: #ccc;
}
div.fr-modal-body div.fr-image-list div.fr-image-container.fr-empty:after {
  position: absolute;
  top: 0;
  bottom: 0;
  content: attr(data-loading);
  display: inline-block;
  height: 20px;
}
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-empty:after {
  margin: auto;
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-empty:after,
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-empty:after {
  left: 0;
  right: 0;
}
div.fr-modal-body div.fr-image-list div.fr-image-container img {
  width: 100%;
  vertical-align: middle;
  position: relative;
  z-index: 2;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -moz-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
  -ms-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
  -o-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
}
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container img {
  -webkit-transition: opacity 0.2s ease 0s, filter 0.2s ease 0s;
}
div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-mobile-selected
  img {
  -webkit-opacity: 0.75;
  -moz-opacity: 0.75;
  opacity: 0.75;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-mobile-selected
  .fr-delete-img,
div.fr-modal-body
  div.fr-image-list
  div.fr-image-container.fr-mobile-selected
  .fr-insert-img {
  display: inline-block;
}
div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img,
div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
  display: none;
  top: 50%;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-background-clip: padding;
  -moz-transition: background 0.2s ease 0s, color 0.2s ease 0s;
  -ms-transition: background 0.2s ease 0s, color 0.2s ease 0s;
  -o-transition: background 0.2s ease 0s, color 0.2s ease 0s;
  box-sizing: border-box;
  position: absolute;
  line-height: 40px;
  text-decoration: none;
  z-index: 3;
}
[dir] div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img,
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-insert-img {
  border-radius: 100%;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  -webkit-transition: background 0.2s ease 0s, color 0.2s ease 0s;
  cursor: pointer;
  margin: 0;
}
div.fr-modal-body div.fr-image-list div.fr-image-container .fr-delete-img {
  fill: #fff;
}
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-delete-img {
  background: #b8312f;
  padding: 8px;
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-delete-img {
  left: 50%;
  -webkit-transform: translateY(-50%) translateX(25%);
  -moz-transform: translateY(-50%) translateX(25%);
  -ms-transform: translateY(-50%) translateX(25%);
  -o-transform: translateY(-50%) translateX(25%);
}
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-delete-img {
  right: 50%;
  -webkit-transform: translateY(-50%) translateX(-25%);
  -moz-transform: translateY(-50%) translateX(-25%);
  -ms-transform: translateY(-50%) translateX(-25%);
  -o-transform: translateY(-50%) translateX(-25%);
}
div.fr-modal-body div.fr-image-list div.fr-image-container .fr-insert-img {
  fill: #0098f7;
}
[dir]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-insert-img {
  background: #fff;
  padding: 8px;
}
[dir="ltr"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-insert-img {
  left: 50%;
  -webkit-transform: translateY(-50%) translateX(-125%);
  -moz-transform: translateY(-50%) translateX(-125%);
  -ms-transform: translateY(-50%) translateX(-125%);
  -o-transform: translateY(-50%) translateX(-125%);
}
[dir="rtl"]
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-insert-img {
  right: 50%;
  -webkit-transform: translateY(-50%) translateX(125%);
  -moz-transform: translateY(-50%) translateX(125%);
  -ms-transform: translateY(-50%) translateX(125%);
  -o-transform: translateY(-50%) translateX(125%);
}
.fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container:hover
  img {
  -webkit-opacity: 0.75;
  -moz-opacity: 0.75;
  opacity: 0.75;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
.fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container:hover
  .fr-delete-img,
.fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container:hover
  .fr-insert-img {
  display: inline-block;
  width: 40px;
  height: 40px;
}
.fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-delete-img:hover {
  color: #fff;
}
[dir]
  .fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-delete-img:hover {
  background: #bf4644;
}
[dir]
  .fr-desktop
  .fr-modal-wrapper
  div.fr-modal-body
  div.fr-image-list
  div.fr-image-container
  .fr-insert-img:hover {
  background: #ebebeb;
}
.fr-line-breaker {
  position: fixed;
  z-index: 2;
  display: none;
}
[dir] .fr-line-breaker {
  cursor: text;
  border-top: 1px solid #0098f7;
}
.fr-line-breaker.fr-visible {
  display: block;
}
.fr-line-breaker a.fr-floating-btn {
  position: absolute;
  top: -20px;
}
[dir="ltr"] .fr-line-breaker a.fr-floating-btn {
  left: calc(50% - 20px);
}
[dir="rtl"] .fr-line-breaker a.fr-floating-btn {
  right: calc(50% - 20px);
}
.fr-line-breaker a.fr-floating-btn svg {
  height: 24px;
  width: 24px;
}
[dir] .fr-line-breaker a.fr-floating-btn svg {
  margin: 8px;
}
.fr-quick-insert {
  position: absolute;
  z-index: 2147483639;
  white-space: nowrap;
  box-sizing: content-box;
}
[dir="ltr"] .fr-quick-insert {
  padding-right: 10px;
}
[dir="rtl"] .fr-quick-insert {
  padding-left: 10px;
}
.fr-quick-insert a.fr-floating-btn svg {
  width: 24px;
  height: 24px;
}
[dir] .fr-quick-insert a.fr-floating-btn svg {
  margin: 8px;
}
[dir="ltr"] .fr-quick-insert.fr-on a.fr-floating-btn svg {
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  -o-transform: rotate(135deg);
}
[dir="rtl"] .fr-quick-insert.fr-on a.fr-floating-btn svg {
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
}
.fr-quick-insert.fr-hidden {
  display: none;
}
.fr-qi-helper {
  position: absolute;
  z-index: 3;
  white-space: nowrap;
}
[dir="ltr"] .fr-qi-helper {
  padding-left: 20px;
}
[dir="rtl"] .fr-qi-helper {
  padding-right: 20px;
}
.fr-qi-helper a.fr-btn.fr-floating-btn {
  display: inline-block;
  color: #222;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
}
[dir] .fr-qi-helper a.fr-btn.fr-floating-btn {
  text-align: center;
  padding: 6px 10px 10px;
  background: #fff;
}
.fr-qi-helper a.fr-btn.fr-floating-btn svg {
  fill: #222;
}
.fr-qi-helper a.fr-btn.fr-floating-btn.fr-size-1 {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
}
[dir] .fr-popup .fr-buttons.fr-tabs .fr-special-character-category {
  padding: 10px 15px;
}
.fr-popup .fr-buttons.fr-tabs .fr-special-character-category span {
  font-weight: 400;
  font-size: 16px;
}
.fr-popup .fr-special-character {
  width: 24px;
  height: 24px;
}
@media (-ms-high-contrast: none) and (min-width: 768px),
  screen and (-ms-high-contrast: active) and (min-width: 768px) {
  .fr-popup .fr-icon-container.fr-sc-container {
    width: 368px;
  }
}
[dir] .fr-element table td.fr-selected-cell,
[dir] .fr-element table th.fr-selected-cell {
  border: 1px double #0098f7;
}
.fr-element table tr {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.fr-element table td,
.fr-element table th {
  user-select: text;
  -o-user-select: text;
  -moz-user-select: text;
  -khtml-user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
}
.fr-element .fr-no-selection table td,
.fr-element .fr-no-selection table th {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.fr-table-resizer {
  position: absolute;
  z-index: 3;
  display: none;
}
[dir] .fr-table-resizer {
  cursor: col-resize;
}
.fr-table-resizer.fr-moving {
  z-index: 2;
}
.fr-table-resizer div {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
}
[dir="ltr"] .fr-table-resizer div {
  border-right: 1px solid #0098f7;
}
[dir="rtl"] .fr-table-resizer div {
  border-left: 1px solid #0098f7;
}
.fr-no-selection {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
[dir] .fr-popup .fr-table-size {
  margin: 20px;
}
.fr-popup .fr-table-size .fr-table-size-info {
  font-size: 14px;
}
[dir] .fr-popup .fr-table-size .fr-table-size-info {
  text-align: center;
}
.fr-popup .fr-table-size .fr-select-table-size {
  line-height: 0;
  white-space: nowrap;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size {
  padding: 5px;
}
.fr-popup .fr-table-size .fr-select-table-size > span {
  display: inline-block;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size > span {
  background: transparent;
}
[dir="ltr"] .fr-popup .fr-table-size .fr-select-table-size > span {
  padding: 0 4px 4px 0;
}
[dir="rtl"] .fr-popup .fr-table-size .fr-select-table-size > span {
  padding: 0 0 4px 4px;
}
.fr-popup .fr-table-size .fr-select-table-size > span > span {
  display: inline-block;
  width: 18px;
  height: 18px;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size > span > span {
  border: 1px solid #ddd;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size > span.hover {
  background: transparent;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size > span.hover > span {
  background: rgba(0, 152, 247, 0.3);
  border: 1px solid #0098f7;
}
.fr-popup .fr-table-size .fr-select-table-size .new-line:after {
  display: block;
  content: "";
  height: 0;
}
[dir] .fr-popup .fr-table-size .fr-select-table-size .new-line:after {
  clear: both;
}
.fr-popup.fr-above .fr-table-size .fr-select-table-size > span {
  display: inline-block !important;
}
.fr-popup .fr-table-colors {
  display: block;
}
[dir] .fr-popup .fr-table-colors {
  padding: 20px 20px 0;
}
.fr-popup.fr-desktop .fr-table-size .fr-select-table-size > span > span {
  width: 12px;
  height: 12px;
}
.fr-insert-helper {
  position: absolute;
  z-index: 9999;
  white-space: nowrap;
}
.fr-element .fr-video {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.fr-element .fr-video:after {
  position: absolute;
  content: "";
  z-index: 1;
  top: 0;
  bottom: 0;
  display: block;
}
[dir] .fr-element .fr-video:after {
  cursor: pointer;
  background: transparent;
}
[dir="ltr"] .fr-element .fr-video:after,
[dir="rtl"] .fr-element .fr-video:after {
  left: 0;
  right: 0;
}
.fr-element .fr-video.fr-active > * {
  z-index: 2;
  position: relative;
}
.fr-element .fr-video > * {
  box-sizing: content-box;
  max-width: 100%;
}
[dir] .fr-element .fr-video > * {
  border: none;
}
.fr-box .fr-video-resizer {
  position: absolute;
  display: none;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
[dir] .fr-box .fr-video-resizer {
  border: 1px solid #0098f7;
}
.fr-box .fr-video-resizer.fr-active {
  display: block;
}
.fr-box .fr-video-resizer .fr-handler {
  display: block;
  position: absolute;
  z-index: 4;
  box-sizing: border-box;
}
[dir] .fr-box .fr-video-resizer .fr-handler {
  background: #0098f7;
  border: 1px solid #fff;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
  cursor: nw-resize;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hne,
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
  cursor: ne-resize;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hne {
  cursor: nw-resize;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
  cursor: sw-resize;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hse,
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
  cursor: se-resize;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hse {
  cursor: sw-resize;
}
.fr-box .fr-video-resizer .fr-handler {
  width: 12px;
  height: 12px;
}
.fr-box .fr-video-resizer .fr-handler.fr-hnw {
  top: -6px;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
  left: -6px;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
  right: -6px;
}
.fr-box .fr-video-resizer .fr-handler.fr-hne {
  top: -6px;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hne {
  right: -6px;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hne {
  left: -6px;
}
.fr-box .fr-video-resizer .fr-handler.fr-hsw {
  bottom: -6px;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
  left: -6px;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
  right: -6px;
}
.fr-box .fr-video-resizer .fr-handler.fr-hse {
  bottom: -6px;
}
[dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hse {
  right: -6px;
}
[dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hse {
  left: -6px;
}
@media (min-width: 1200px) {
  .fr-box .fr-video-resizer .fr-handler {
    width: 10px;
    height: 10px;
  }
  .fr-box .fr-video-resizer .fr-handler.fr-hnw {
    top: -5px;
  }
  [dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
    left: -5px;
  }
  [dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hnw {
    right: -5px;
  }
  .fr-box .fr-video-resizer .fr-handler.fr-hne {
    top: -5px;
  }
  [dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hne {
    right: -5px;
  }
  [dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hne {
    left: -5px;
  }
  .fr-box .fr-video-resizer .fr-handler.fr-hsw {
    bottom: -5px;
  }
  [dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
    left: -5px;
  }
  [dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hsw {
    right: -5px;
  }
  .fr-box .fr-video-resizer .fr-handler.fr-hse {
    bottom: -5px;
  }
  [dir="ltr"] .fr-box .fr-video-resizer .fr-handler.fr-hse {
    right: -5px;
  }
  [dir="rtl"] .fr-box .fr-video-resizer .fr-handler.fr-hse {
    left: -5px;
  }
}
.fr-popup .fr-video-size-layer .fr-video-group .fr-input-line {
  width: calc(50% - 5px);
  display: inline-block;
}
[dir="ltr"]
  .fr-popup
  .fr-video-size-layer
  .fr-video-group
  .fr-input-line
  + .fr-input-line {
  margin-left: 10px;
}
[dir="rtl"]
  .fr-popup
  .fr-video-size-layer
  .fr-video-group
  .fr-input-line
  + .fr-input-line {
  margin-right: 10px;
}
.fr-popup .fr-video-upload-layer {
  position: relative;
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 140%;
}
[dir] .fr-popup .fr-video-upload-layer {
  border: 2px dashed #bdbdbd;
  padding: 25px 0;
  margin: 20px;
  text-align: center;
}
[dir] .fr-popup .fr-video-upload-layer:hover {
  background: #ebebeb;
}
[dir] .fr-popup .fr-video-upload-layer.fr-drop {
  background: #ebebeb;
  border-color: #0098f7;
}
.fr-popup .fr-video-upload-layer .fr-form {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 2147483640;
  overflow: hidden;
  width: 100% !important;
}
[dir] .fr-popup .fr-video-upload-layer .fr-form {
  margin: 0 !important;
  padding: 0 !important;
}
[dir="ltr"] .fr-popup .fr-video-upload-layer .fr-form,
[dir="rtl"] .fr-popup .fr-video-upload-layer .fr-form {
  left: 0;
  right: 0;
}
.fr-popup .fr-video-upload-layer .fr-form input {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 500%;
  height: 100%;
  font-size: 400px;
}
[dir] .fr-popup .fr-video-upload-layer .fr-form input {
  cursor: pointer;
  margin: 0;
}
[dir="ltr"] .fr-popup .fr-video-upload-layer .fr-form input {
  right: 0;
}
[dir="rtl"] .fr-popup .fr-video-upload-layer .fr-form input {
  left: 0;
}
.fr-popup .fr-video-progress-bar-layer > h3 {
  font-size: 16px;
  font-weight: 400;
}
[dir] .fr-popup .fr-video-progress-bar-layer > h3 {
  margin: 10px 0;
}
.fr-popup .fr-video-progress-bar-layer > div.fr-action-buttons {
  display: none;
}
.fr-popup .fr-video-progress-bar-layer > div.fr-loader {
  height: 10px;
  width: 100%;
  overflow: hidden;
  position: relative;
}
[dir] .fr-popup .fr-video-progress-bar-layer > div.fr-loader {
  background: #b3e0fd;
  margin-top: 20px;
}
.fr-popup .fr-video-progress-bar-layer > div.fr-loader span {
  display: block;
  height: 100%;
  width: 0;
  -moz-transition: width 0.2s ease 0s;
  -ms-transition: width 0.2s ease 0s;
  -o-transition: width 0.2s ease 0s;
}
[dir] .fr-popup .fr-video-progress-bar-layer > div.fr-loader span {
  background: #0098f7;
  -webkit-transition: width 0.2s ease 0s;
}
.fr-popup .fr-video-progress-bar-layer > div.fr-loader.fr-indeterminate span {
  width: 30% !important;
  position: absolute;
  top: 0;
}
[dir="ltr"]
  .fr-popup
  .fr-video-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-ltr 2s linear infinite;
  animation: loading-ltr 2s linear infinite;
}
[dir="rtl"]
  .fr-popup
  .fr-video-progress-bar-layer
  > div.fr-loader.fr-indeterminate
  span {
  -webkit-animation: loading-rtl 2s linear infinite;
  animation: loading-rtl 2s linear infinite;
}
.fr-popup .fr-video-progress-bar-layer.fr-error > div.fr-loader {
  display: none;
}
.fr-popup .fr-video-progress-bar-layer.fr-error > div.fr-action-buttons {
  display: block;
}
.fr-video-overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 2147483640;
  display: none;
}
[dir="ltr"] .fr-video-overlay,
[dir="rtl"] .fr-video-overlay {
  left: 0;
  right: 0;
}
.clearfix:after {
  display: block;
  content: "";
  height: 0;
}
[dir] .clearfix:after {
  clear: both;
}
.hide-by-clipping {
  position: absolute;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
}
[dir] .hide-by-clipping {
  padding: 0;
  margin: -1px;
  border: 0;
}
.fr-img-caption.fr-rounded img,
img.fr-rounded {
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-background-clip: padding;
}
[dir] .fr-img-caption.fr-rounded img,
[dir] img.fr-rounded {
  border-radius: 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
[dir] .fr-img-caption.fr-bordered img,
[dir] img.fr-bordered {
  border: 5px solid #ccc;
}
img.fr-bordered {
  box-sizing: content-box;
}
.fr-img-caption.fr-bordered img {
  box-sizing: border-box;
}
.fr-view {
  word-wrap: break-word;
}
.fr-view span[style~="color:"] a {
  color: inherit;
}
.fr-view strong {
  font-weight: 700;
}
.fr-view table {
  border-collapse: collapse;
  empty-cells: show;
  max-width: 100%;
}
[dir] .fr-view table {
  border: none;
}
.fr-view table td {
  min-width: 5px;
}
[dir] .fr-view table.fr-dashed-borders td,
[dir] .fr-view table.fr-dashed-borders th {
  border-style: dashed;
}
[dir] .fr-view table.fr-alternate-rows tbody tr:nth-child(2n) {
  background: #f5f5f5;
}
[dir] .fr-view table td,
[dir] .fr-view table th {
  border: 1px solid #ddd;
}
.fr-view table td:empty,
.fr-view table th:empty {
  height: 20px;
}
[dir] .fr-view table td.fr-highlighted,
[dir] .fr-view table th.fr-highlighted {
  border: 1px double red;
}
[dir] .fr-view table td.fr-thick,
[dir] .fr-view table th.fr-thick {
  border-width: 2px;
}
[dir] .fr-view table th {
  background: #ececec;
}
.fr-view hr {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -moz-column-break-after: always;
  break-after: always;
  page-break-after: always;
}
[dir] .fr-view hr {
  clear: both;
}
.fr-view .fr-file {
  position: relative;
}
.fr-view .fr-file:after {
  position: relative;
  content: "\1F4CE";
  font-weight: 400;
}
.fr-view pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: visible;
}
.fr-view[dir="rtl"] blockquote {
  border-left: none;
  border-right: 2px solid #5e35b1;
  margin-right: 0;
  padding-right: 5px;
  padding-left: 0;
}
.fr-view[dir="rtl"] blockquote blockquote {
  border-color: #00bcd4;
}
.fr-view[dir="rtl"] blockquote blockquote blockquote {
  border-color: #43a047;
}
.fr-view blockquote {
  color: #5e35b1;
}
[dir="ltr"] .fr-view blockquote {
  border-left: 2px solid #5e35b1;
  margin-left: 0;
  padding-left: 5px;
}
[dir="rtl"] .fr-view blockquote {
  border-right: 2px solid #5e35b1;
  margin-right: 0;
  padding-right: 5px;
}
.fr-view blockquote blockquote {
  color: #00bcd4;
}
[dir] .fr-view blockquote blockquote {
  border-color: #00bcd4;
}
.fr-view blockquote blockquote blockquote {
  color: #43a047;
}
[dir] .fr-view blockquote blockquote blockquote {
  border-color: #43a047;
}
.fr-view span.fr-emoticon {
  font-weight: 400;
  font-family: Apple Color Emoji, Segoe UI Emoji, NotoColorEmoji,
    Segoe UI Symbol, Android Emoji, EmojiSymbols;
  display: inline;
  line-height: 0;
}
.fr-view span.fr-emoticon.fr-emoticon-img {
  font-size: inherit;
  height: 1em;
  width: 1em;
  min-height: 20px;
  min-width: 20px;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}
[dir] .fr-view span.fr-emoticon.fr-emoticon-img {
  background-repeat: no-repeat !important;
  margin: -0.1em 0.1em 0.1em;
}
.fr-view .fr-text-gray {
  color: #aaa !important;
}
[dir] .fr-view .fr-text-bordered {
  border-top: 1px solid #222;
  border-bottom: 1px solid #222;
  padding: 10px 0;
}
.fr-view .fr-text-spaced {
  letter-spacing: 1px;
}
.fr-view .fr-text-uppercase {
  text-transform: uppercase;
}
[dir] .fr-view .fr-class-highlighted {
  background-color: #ff0;
}
.fr-view .fr-class-code {
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-background-clip: padding;
  font-family: Courier New, Courier, monospace;
}
[dir] .fr-view .fr-class-code {
  border-color: #ccc;
  border-radius: 2px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  background: #f5f5f5;
  padding: 10px;
}
.fr-view .fr-class-transparency {
  opacity: 0.5;
}
.fr-view img {
  position: relative;
  max-width: 100%;
}
.fr-view img.fr-dib {
  display: block;
  vertical-align: top;
}
[dir] .fr-view img.fr-dib {
  margin: 5px auto;
  float: none;
}
[dir="ltr"] .fr-view img.fr-dib.fr-fil {
  margin-left: 0;
  text-align: left;
}
[dir="ltr"] .fr-view img.fr-dib.fr-fir,
[dir="rtl"] .fr-view img.fr-dib.fr-fil {
  margin-right: 0;
  text-align: right;
}
[dir="rtl"] .fr-view img.fr-dib.fr-fir {
  margin-left: 0;
  text-align: left;
}
.fr-view img.fr-dii {
  display: inline-block;
  vertical-align: bottom;
  max-width: calc(100% - 10px);
}
[dir] .fr-view img.fr-dii {
  float: none;
}
[dir="ltr"] .fr-view img.fr-dii,
[dir="rtl"] .fr-view img.fr-dii {
  margin-left: 5px;
  margin-right: 5px;
}
.fr-view img.fr-dii.fr-fil {
  max-width: calc(100% - 5px);
}
[dir="ltr"] .fr-view img.fr-dii.fr-fil {
  float: left;
  margin: 5px 5px 5px 0;
}
[dir="rtl"] .fr-view img.fr-dii.fr-fil {
  float: right;
  margin: 5px 0 5px 5px;
}
.fr-view img.fr-dii.fr-fir {
  max-width: calc(100% - 5px);
}
[dir="ltr"] .fr-view img.fr-dii.fr-fir {
  float: right;
  margin: 5px 0 5px 5px;
}
[dir="rtl"] .fr-view img.fr-dii.fr-fir {
  float: left;
  margin: 5px 5px 5px 0;
}
.fr-view span.fr-img-caption {
  position: relative;
  max-width: 100%;
}
.fr-view span.fr-img-caption.fr-dib {
  display: block;
  vertical-align: top;
}
[dir] .fr-view span.fr-img-caption.fr-dib {
  margin: 5px auto;
  float: none;
}
[dir="ltr"] .fr-view span.fr-img-caption.fr-dib.fr-fil {
  margin-left: 0;
  text-align: left;
}
[dir="ltr"] .fr-view span.fr-img-caption.fr-dib.fr-fir,
[dir="rtl"] .fr-view span.fr-img-caption.fr-dib.fr-fil {
  margin-right: 0;
  text-align: right;
}
[dir="rtl"] .fr-view span.fr-img-caption.fr-dib.fr-fir {
  margin-left: 0;
  text-align: left;
}
.fr-view span.fr-img-caption.fr-dii {
  display: inline-block;
  vertical-align: bottom;
  max-width: calc(100% - 10px);
}
[dir] .fr-view span.fr-img-caption.fr-dii {
  float: none;
}
[dir="ltr"] .fr-view span.fr-img-caption.fr-dii,
[dir="rtl"] .fr-view span.fr-img-caption.fr-dii {
  margin-left: 5px;
  margin-right: 5px;
}
.fr-view span.fr-img-caption.fr-dii.fr-fil {
  max-width: calc(100% - 5px);
}
[dir="ltr"] .fr-view span.fr-img-caption.fr-dii.fr-fil {
  float: left;
  margin: 5px 5px 5px 0;
}
[dir="rtl"] .fr-view span.fr-img-caption.fr-dii.fr-fil {
  float: right;
  margin: 5px 0 5px 5px;
}
.fr-view span.fr-img-caption.fr-dii.fr-fir {
  max-width: calc(100% - 5px);
}
[dir="ltr"] .fr-view span.fr-img-caption.fr-dii.fr-fir {
  float: right;
  margin: 5px 0 5px 5px;
}
[dir="rtl"] .fr-view span.fr-img-caption.fr-dii.fr-fir {
  float: left;
  margin: 5px 5px 5px 0;
}
.fr-view .fr-video {
  position: relative;
}
[dir] .fr-view .fr-video {
  text-align: center;
}
.fr-view .fr-video.fr-rv {
  height: 0;
  overflow: hidden;
}
[dir] .fr-view .fr-video.fr-rv {
  padding-bottom: 56.25%;
  padding-top: 30px;
}
.fr-view .fr-video.fr-rv > iframe,
.fr-view .fr-video.fr-rv embed,
.fr-view .fr-video.fr-rv object {
  position: absolute !important;
  top: 0;
  width: 100%;
  height: 100%;
}
[dir="ltr"] .fr-view .fr-video.fr-rv > iframe,
[dir="ltr"] .fr-view .fr-video.fr-rv embed,
[dir="ltr"] .fr-view .fr-video.fr-rv object {
  left: 0;
}
[dir="rtl"] .fr-view .fr-video.fr-rv > iframe,
[dir="rtl"] .fr-view .fr-video.fr-rv embed,
[dir="rtl"] .fr-view .fr-video.fr-rv object {
  right: 0;
}
.fr-view .fr-video > * {
  box-sizing: content-box;
  max-width: 100%;
}
[dir] .fr-view .fr-video > * {
  border: none;
}
.fr-view .fr-video.fr-dvb {
  display: block;
}
[dir] .fr-view .fr-video.fr-dvb {
  clear: both;
}
[dir="ltr"] .fr-view .fr-video.fr-dvb.fr-fvl {
  text-align: left;
}
[dir="ltr"] .fr-view .fr-video.fr-dvb.fr-fvr,
[dir="rtl"] .fr-view .fr-video.fr-dvb.fr-fvl {
  text-align: right;
}
[dir="rtl"] .fr-view .fr-video.fr-dvb.fr-fvr {
  text-align: left;
}
.fr-view .fr-video.fr-dvi {
  display: inline-block;
}
[dir="ltr"] .fr-view .fr-video.fr-dvi.fr-fvl {
  float: left;
}
[dir="ltr"] .fr-view .fr-video.fr-dvi.fr-fvr,
[dir="rtl"] .fr-view .fr-video.fr-dvi.fr-fvl {
  float: right;
}
[dir="rtl"] .fr-view .fr-video.fr-dvi.fr-fvr {
  float: left;
}
.fr-view a.fr-strong {
  font-weight: 700;
}
.fr-view a.fr-green {
  color: green;
}
[dir] .fr-view .fr-img-caption {
  text-align: center;
}
.fr-view .fr-img-caption .fr-img-wrap {
  width: 100%;
}
[dir] .fr-view .fr-img-caption .fr-img-wrap {
  padding: 0;
  margin: auto;
  text-align: center;
}
.fr-view .fr-img-caption .fr-img-wrap img {
  display: block;
  width: 100%;
}
[dir] .fr-view .fr-img-caption .fr-img-wrap img {
  margin: auto;
}
.fr-view .fr-img-caption .fr-img-wrap > span {
  display: block;
  font-size: 14px;
  font-weight: 400;
  box-sizing: border-box;
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  width: 100%;
}
[dir] .fr-view .fr-img-caption .fr-img-wrap > span {
  margin: auto;
  padding: 5px 5px 10px;
  text-align: center;
}
.fr-view button.fr-rounded,
.fr-view input.fr-rounded,
.fr-view textarea.fr-rounded {
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-background-clip: padding;
}
[dir] .fr-view button.fr-rounded,
[dir] .fr-view input.fr-rounded,
[dir] .fr-view textarea.fr-rounded {
  border-radius: 10px;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}
.fr-view button.fr-large,
.fr-view input.fr-large,
.fr-view textarea.fr-large {
  font-size: 24px;
}
a.fr-view.fr-strong {
  font-weight: 700;
}
a.fr-view.fr-green {
  color: green;
}
img.fr-view {
  position: relative;
  max-width: 100%;
}
img.fr-view.fr-dib {
  display: block;
  vertical-align: top;
}
[dir] img.fr-view.fr-dib {
  margin: 5px auto;
  float: none;
}
[dir="ltr"] img.fr-view.fr-dib.fr-fil {
  margin-left: 0;
  text-align: left;
}
[dir="ltr"] img.fr-view.fr-dib.fr-fir,
[dir="rtl"] img.fr-view.fr-dib.fr-fil {
  margin-right: 0;
  text-align: right;
}
[dir="rtl"] img.fr-view.fr-dib.fr-fir {
  margin-left: 0;
  text-align: left;
}
img.fr-view.fr-dii {
  display: inline-block;
  vertical-align: bottom;
  max-width: calc(100% - 10px);
}
[dir] img.fr-view.fr-dii {
  float: none;
}
[dir="ltr"] img.fr-view.fr-dii,
[dir="rtl"] img.fr-view.fr-dii {
  margin-left: 5px;
  margin-right: 5px;
}
img.fr-view.fr-dii.fr-fil {
  max-width: calc(100% - 5px);
}
[dir="ltr"] img.fr-view.fr-dii.fr-fil {
  float: left;
  margin: 5px 5px 5px 0;
}
[dir="rtl"] img.fr-view.fr-dii.fr-fil {
  float: right;
  margin: 5px 0 5px 5px;
}
img.fr-view.fr-dii.fr-fir {
  max-width: calc(100% - 5px);
}
[dir="ltr"] img.fr-view.fr-dii.fr-fir {
  float: right;
  margin: 5px 0 5px 5px;
}
[dir="rtl"] img.fr-view.fr-dii.fr-fir {
  float: left;
  margin: 5px 5px 5px 0;
}
span.fr-img-caption.fr-view {
  position: relative;
  max-width: 100%;
}
span.fr-img-caption.fr-view.fr-dib {
  display: block;
  vertical-align: top;
}
[dir] span.fr-img-caption.fr-view.fr-dib {
  margin: 5px auto;
  float: none;
}
[dir="ltr"] span.fr-img-caption.fr-view.fr-dib.fr-fil {
  margin-left: 0;
  text-align: left;
}
[dir="ltr"] span.fr-img-caption.fr-view.fr-dib.fr-fir,
[dir="rtl"] span.fr-img-caption.fr-view.fr-dib.fr-fil {
  margin-right: 0;
  text-align: right;
}
[dir="rtl"] span.fr-img-caption.fr-view.fr-dib.fr-fir {
  margin-left: 0;
  text-align: left;
}
span.fr-img-caption.fr-view.fr-dii {
  display: inline-block;
  vertical-align: bottom;
  max-width: calc(100% - 10px);
}
[dir] span.fr-img-caption.fr-view.fr-dii {
  float: none;
}
[dir="ltr"] span.fr-img-caption.fr-view.fr-dii,
[dir="rtl"] span.fr-img-caption.fr-view.fr-dii {
  margin-left: 5px;
  margin-right: 5px;
}
span.fr-img-caption.fr-view.fr-dii.fr-fil {
  max-width: calc(100% - 5px);
}
[dir="ltr"] span.fr-img-caption.fr-view.fr-dii.fr-fil {
  float: left;
  margin: 5px 5px 5px 0;
}
[dir="rtl"] span.fr-img-caption.fr-view.fr-dii.fr-fil {
  float: right;
  margin: 5px 0 5px 5px;
}
span.fr-img-caption.fr-view.fr-dii.fr-fir {
  max-width: calc(100% - 5px);
}
[dir="ltr"] span.fr-img-caption.fr-view.fr-dii.fr-fir {
  float: right;
  margin: 5px 0 5px 5px;
}
[dir="rtl"] span.fr-img-caption.fr-view.fr-dii.fr-fir {
  float: left;
  margin: 5px 5px 5px 0;
}
[dir] .editor-content-section .fr-view p {
  margin: 0;
}
[dir] .editor-content-section .fr-view p:not(:last-child) {
  margin-bottom: 15px;
}
.sales-notifcation {
  position: fixed;
  bottom: 20px;
  min-width: 300px;
  max-width: 600px;
  z-index: 5;
}
[dir] .sales-notifcation {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
[dir="ltr"] .sales-notifcation {
  left: 20px;
}
[dir="rtl"] .sales-notifcation {
  right: 20px;
}
@media (max-width: 425px) {
  .sales-notifcation {
    min-width: calc(100% - 30px);
    bottom: 15px;
  }
  [dir="ltr"] .sales-notifcation {
    left: 15px;
  }
  [dir="rtl"] .sales-notifcation {
    right: 15px;
  }
}
.sales-notifcation.hidden {
  display: none;
}
.sale {
  position: relative;
  display: flex;
  align-items: flex-start;
}
[dir] .sale {
  padding: 10px;
}
.sale .close {
  position: absolute;
  top: -10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  font-size: 18px;
  z-index: 2;
  transition: color 0.25s;
}
[dir] .sale .close {
  background-color: #fff;
  cursor: pointer;
  border-radius: 3px;
}
[dir="ltr"] .sale .close {
  right: -10px;
}
[dir="rtl"] .sale .close {
  left: -10px;
}
.sale .close:hover {
  color: var(--primary-color);
}
.sale .sale-thumbnail {
  position: relative;
  width: 65px;
  height: 65px;
  text-indent: -9999px;
}
[dir] .sale .sale-thumbnail {
  border: 1px solid #f0f0f0;
  background-image: url(/store-front/images/product-default.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50%;
  border-radius: 3px;
}
[dir="ltr"] .sale .sale-thumbnail {
  margin: 0 10px 0 0;
}
[dir="rtl"] .sale .sale-thumbnail {
  margin: 0 0 0 10px;
}
.sale .sale-thumbnail img {
  position: absolute;
  top: 50%;
  z-index: 1;
}
[dir="ltr"] .sale .sale-thumbnail img {
  left: 50%;
  transform: translate(-50%, -50%);
}
[dir="rtl"] .sale .sale-thumbnail img {
  right: 50%;
  transform: translate(50%, -50%);
}
.sale .sale-body {
  width: calc(100% - 75px);
}
.sale .close,
.sale .date,
.sale .message {
  color: #696969;
}
.sale .message,
.sale .name {
  font-size: 13px;
}
.sale .name {
  font-weight: 600;
  text-transform: capitalize;
  color: var(--primary-color);
}
[dir] .sale .name {
  margin: 4px 0;
}
.sale .date {
  font-size: 12px;
}
.sale .sale-link {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
[dir="ltr"] .sale .sale-link {
  left: 0;
}
[dir="rtl"] .sale .sale-link {
  right: 0;
}
.cookie-consent {
  position: fixed;
  bottom: 20px;
  width: 300px;
  z-index: 1;
}
[dir] .cookie-consent {
  background-color: #fff;
  text-align: center;
  padding: 12px 0;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
}
[dir="ltr"] .cookie-consent {
  right: 20px;
}
[dir="rtl"] .cookie-consent {
  left: 20px;
}
@media (min-width: 1124px) {
  [dir] .cookie-consent {
    padding: 12px;
  }
}
@media (max-width: 425px) {
  .cookie-consent {
    width: calc(100% - 30px);
  }
  [dir="ltr"] .cookie-consent {
    right: 15px;
  }
  [dir="rtl"] .cookie-consent {
    left: 15px;
  }
}
.cookie-consent .container {
  width: 100%;
}
.cookie-consent .fr-view a {
  font-weight: 600;
  text-transform: capitalize;
}
[dir="ltr"] .cookie-consent .fr-view a {
  margin: 0 0 0 1px;
}
[dir="rtl"] .cookie-consent .fr-view a {
  margin: 0 1px 0 0;
}
.cookie-consent .cookie-actions {
  width: 100%;
}
[dir] .cookie-consent .cookie-actions {
  margin: 10px 0 0;
}
.cookie-consent .cookie-actions .button {
  width: 100%;
}
.cookie-consent .cookie-actions .button .yc {
  display: block;
  font-size: 18px;
}
[dir] .cookie-consent .cookie-actions .button .yc {
  padding: 1px 0 0;
}
.cart-section h1 {
  font-size: 26px;
  line-height: 36px;
}
@media (max-width: 768px) {
  .cart-section h1 {
    font-size: 22px;
    line-height: 32px;
  }
}
.cart-section .empty-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
[dir] .cart-section .empty-cart {
  text-align: center;
}
@media (min-width: 768px) {
  .cart-section .empty-cart img {
    max-width: 650px;
  }
}
[dir] .cart-section .empty-cart h1 {
  margin: 24px 0;
}
.cart-section .cart .cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .cart-section .cart .cart-header {
  margin: 0 0 24px;
}
@media (max-width: 425px) {
  .cart-section .cart .cart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  [dir] .cart-section .cart .cart-header h1 {
    margin: 0 0 15px;
  }
}
.cart-section .cart .cart-header .button {
  width: 30%;
}
@media (max-width: 425px) {
  .cart-section .cart .cart-header .button {
    width: 100%;
  }
}
[dir] .cart-section .cart .cart-body {
  padding: 30px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
@media (max-width: 768px) {
  [dir] .cart-section .cart .cart-body {
    padding: 20px 15px;
  }
}
@media (max-width: 425px) {
  [dir] .cart-section .cart .cart-body {
    padding: 0;
    border: 0;
  }
}
.cart-section .cart .cart-total {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}
[dir] .cart-section .cart .cart-total {
  padding: 30px 0;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-total {
    flex-direction: column;
  }
  [dir] .cart-section .cart .cart-total {
    padding: 20px 0;
  }
}
.cart-section .cart .cart-item:not(.coupon-item) {
  width: calc(33.3% - 26px);
}
@media (max-width: 768px) {
  .cart-section .cart .cart-item:not(.coupon-item) {
    width: 100%;
  }
}
@media (max-width: 768px) {
  [dir] .cart-section .cart .cart-item:not(:last-child) {
    margin: 0 0 20px;
  }
}
.cart-section .cart .cart-item .form-label {
  display: block;
}
[dir] .cart-section .cart .cart-item .form-label {
  margin: 0 0 8px;
}
.cart-section .cart .cart-item textarea {
  display: block;
}
.cart-section .cart .cart-item .total-items,
.cart-section .cart .cart-item textarea {
  min-height: 95px;
}
[dir] .cart-section .cart .cart-item .total-items {
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  padding: 25px 15px;
}
.cart-section .cart .cart-item .total-item .title {
  font-size: 15px;
}
[dir="ltr"] .cart-section .cart .cart-item .total-item .title {
  margin: 0 10px 0 0;
}
[dir="rtl"] .cart-section .cart .cart-item .total-item .title {
  margin: 0 0 0 10px;
}
[dir] .cart-section .cart .cart-item .total-item:not(:last-child) {
  margin: 0 0 10px;
}
.cart-section .cart .cart-item .cart-main-total .currency-value,
.cart-section .cart .cart-item .cart-main-total .title {
  font-size: 16px;
  font-weight: 500;
}
.cart-section .cart .cart-item .cart-main-total .currency-value {
  color: var(--primary-color);
}
[dir] .cart-section .cart .cart-item .cart-actions {
  margin: 20px 0 0;
}
.cart-section .cart .cart-item .cart-actions .button {
  width: 100%;
}
.cart-section .cart .cart-item .coupon-actions {
  position: relative;
}
.cart-section .cart .cart-item .coupon-actions input {
  height: 40px;
}
.cart-section .cart .cart-item .coupon-actions .button {
  position: absolute;
  top: 0;
  height: 100%;
}
[dir] .cart-section .cart .cart-item .coupon-actions .button {
  padding: 8px 12px 9px;
}
[dir="ltr"] .cart-section .cart .cart-item .coupon-actions .button {
  right: 0;
  border-radius: 0 3px 3px 0;
}
[dir="rtl"] .cart-section .cart .cart-item .coupon-actions .button {
  left: 0;
  border-radius: 3px 0 0 3px;
}
.cart-section .cart .cart-item .coupon-actions .button:hover {
  color: #fff;
}
[dir] .cart-section .cart .cart-item .coupon-actions .button:hover {
  background-color: var(--primary-color);
}
.cart-section .cart .cart-item .coupon-actions .button .yc {
  display: block;
  font-size: 16px;
}
.cart-section .cart .cart-item .coupon-message {
  color: var(--primary-color);
}
[dir] .cart-section .cart .cart-item .coupon-message {
  margin: 5px 0 0;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}
.cart-section .cart .coupon-item {
  width: 250px;
}
@media (max-width: 768px) {
  .cart-section .cart .coupon-item {
    width: 100%;
  }
}
[dir] .cart-section .cart .cart-footer {
  padding: 30px 0 0;
  border-top: 1px solid #f0f0f0;
}
[dir] .cart-section .cart .cart-footer img {
  margin: auto;
}
@media (max-width: 768px) {
  [dir] .cart-section .cart .cart-footer {
    padding: 20px 0 0;
  }
}
@media (max-width: 425px) {
  [dir] .cart-section .cart .cart-footer {
    padding: 30px 0 0;
  }
}
.cart-section .cart .cart-table .table-body,
.cart-section .cart .cart-table .table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .cart-section .cart .cart-table .table-body,
[dir] .cart-section .cart .cart-table .table-header {
  border-bottom: 1px solid #f0f0f0;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-table .table-header {
    display: none;
  }
  .cart-section .cart .cart-table .table-body {
    flex-direction: column;
  }
  [dir] .cart-section .cart .cart-table .table-body {
    border: 1px solid #f0f0f0;
    border-radius: 3px;
  }
  [dir] .cart-section .cart .cart-table .table-body:not(:last-child) {
    margin: 0 0 15px;
  }
}
.cart-section .cart .cart-table .cart-border {
  font-size: 13px;
}
[dir] .cart-section .cart .cart-table .cart-border {
  padding: 0 0 15px;
}
.cart-section .cart .cart-table .cart-border,
.cart-section .cart .cart-table .table-item {
  width: calc(100% - 540px);
  font-weight: 500;
}
[dir] .cart-section .cart .cart-table .cart-border,
[dir] .cart-section .cart .cart-table .table-item {
  text-align: center;
}
[dir="ltr"] .cart-section .cart .cart-table .cart-border,
[dir="ltr"] .cart-section .cart .cart-table .table-item {
  border-right: 1px solid #f0f0f0;
}
[dir="rtl"] .cart-section .cart .cart-table .cart-border,
[dir="rtl"] .cart-section .cart .cart-table .table-item {
  border-left: 1px solid #f0f0f0;
}
.cart-section .cart .cart-table .cart-border.width-100,
.cart-section .cart .cart-table .table-item.width-100 {
  width: 100px;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-table .cart-border.width-100,
  .cart-section .cart .cart-table .table-item.width-100 {
    width: 100%;
  }
}
[dir="ltr"] .cart-section .cart .cart-table .cart-border:nth-child(2),
[dir="ltr"] .cart-section .cart .cart-table .table-item:nth-child(2) {
  border-left: 1px solid #f0f0f0;
}
[dir="rtl"] .cart-section .cart .cart-table .cart-border:nth-child(2),
[dir="rtl"] .cart-section .cart .cart-table .table-item:nth-child(2) {
  border-right: 1px solid #f0f0f0;
}
@media (max-width: 768px) {
  [dir] .cart-section .cart .cart-table .cart-border:nth-child(2),
  [dir] .cart-section .cart .cart-table .table-item:nth-child(2) {
    border: 0;
  }
}
[dir] .cart-section .cart .cart-table .cart-border:first-child,
[dir] .cart-section .cart .cart-table .cart-border:last-child,
[dir] .cart-section .cart .cart-table .table-item:first-child,
[dir] .cart-section .cart .cart-table .table-item:last-child {
  border: 0;
}
.cart-section .cart .cart-table .cart-border.width-80,
.cart-section .cart .cart-table .table-item.width-80 {
  width: 80px;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-table .cart-border.width-80,
  .cart-section .cart .cart-table .table-item.width-80 {
    width: 100%;
  }
}
.cart-section .cart .cart-table .cart-border.width-180,
.cart-section .cart .cart-table .table-item.width-180 {
  width: 180px;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-table .cart-border.width-180,
  .cart-section .cart .cart-table .table-item.width-180 {
    width: 100%;
  }
}
.cart-section .cart .cart-table .table-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  font-size: 14px;
}
.cart-section .cart .cart-table .table-item a {
  max-width: 100%;
}
[dir] .cart-section .cart .cart-table .table-item a {
  padding: 0 10px;
}
@media (min-width: 768px) {
  .cart-section .cart .cart-table .table-item a {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.cart-section .cart .cart-table .table-item button {
  width: 40px;
  height: 40px;
  font-size: 18px;
}
.cart-section .cart .cart-table .table-item .currency-value {
  color: var(--primary-color);
}
.cart-section .cart .cart-table .table-item .quantity {
  position: relative;
  width: 110px;
}
.cart-section .cart .cart-table .table-item .quantity .quantity-handler {
  width: 26px;
  height: 26px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
  position: absolute;
  top: 50%;
}
[dir] .cart-section .cart .cart-table .table-item .quantity .quantity-handler {
  text-align: center;
  cursor: pointer;
  transform: translateY(-50%);
  background-color: #f0f0f0;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
[dir="ltr"]
  .cart-section
  .cart
  .cart-table
  .table-item
  .quantity
  .quantity-handler-left {
  left: 0;
}
[dir="ltr"]
  .cart-section
  .cart
  .cart-table
  .table-item
  .quantity
  .quantity-handler-right,
[dir="rtl"]
  .cart-section
  .cart
  .cart-table
  .table-item
  .quantity
  .quantity-handler-left {
  right: 0;
}
[dir="rtl"]
  .cart-section
  .cart
  .cart-table
  .table-item
  .quantity
  .quantity-handler-right {
  left: 0;
}
.cart-section .cart .cart-table .table-item .quantity input {
  width: 100%;
  height: 26px;
  font-size: 14px;
  font-weight: 500;
}
[dir] .cart-section .cart .cart-table .table-item .quantity input {
  padding: 0;
  text-align: center;
  border: 0;
  box-shadow: none;
}
@media (max-width: 768px) {
  .cart-section .cart .cart-table .table-item {
    width: 100%;
    flex-direction: column;
    height: auto;
  }
  [dir] .cart-section .cart .cart-table .table-item {
    border: 0;
  }
  [dir] .cart-section .cart .cart-table .table-item:not(:last-child) {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
  }
}
.cart-section .cart .cart-table .table-thumbnail img {
  width: 85%;
}
.cart-section .cart .mobile-view {
  font-size: 13px;
}
[dir] .cart-section .cart .mobile-view {
  margin: 0 0 5px;
}
@media (min-width: 768px) {
  .cart-section .cart .mobile-view {
    display: none;
  }
}
@media (min-width: 425px) {
  .auth-section .auth-form {
    width: 500px;
  }
  [dir] .auth-section .auth-form {
    margin: auto;
  }
}
[dir] .auth-section .form-group:not(:last-child) {
  margin: 0 0 18px;
}
.auth-section .form-group.has-icon {
  position: relative;
}
.auth-section .form-group.has-icon .yc,
.auth-section .form-group.has-icon a {
  position: absolute;
  top: 15px;
}
[dir="ltr"] .auth-section .form-group.has-icon .yc,
[dir="ltr"] .auth-section .form-group.has-icon a {
  right: 15px;
}
[dir="rtl"] .auth-section .form-group.has-icon .yc,
[dir="rtl"] .auth-section .form-group.has-icon a {
  left: 15px;
}
.auth-section .form-group.has-icon .yc {
  color: #ccc;
  font-size: 18px;
}
.auth-section .flex-form-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .auth-section .has-error input {
  border-color: var(--danger-color);
  border-width: 2px;
}
.auth-section .validate-error {
  display: block;
  color: var(--danger-color);
}
[dir] .auth-section .validate-error {
  margin: 5px 0 0;
}
.auth-section .captcha-code {
  display: block;
  height: 50px;
}
[dir] .auth-section .captcha-code {
  border-radius: 3px;
}
[dir="ltr"] .auth-section .captcha-code {
  margin: 0 15px 0 0;
}
[dir="rtl"] .auth-section .captcha-code {
  margin: 0 0 0 15px;
}
@media (max-width: 425px) {
  .auth-section .captcha-code {
    width: 100px;
  }
}
.auth-section .checkbox {
  flex: auto;
}
.auth-section .button {
  width: 100%;
}
.auth-section .grid-form-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 18px;
}
[dir] .auth-section .grid-form-group {
  margin: 0 0 18px;
}
[dir] .auth-section .grid-form-group .form-group {
  margin: 0;
}
@media (max-width: 425px) {
  .auth-section .grid-form-group {
    grid-template-columns: repeat(1, 1fr);
  }
}
.auth-section .flex-end-group {
  display: flex;
  justify-content: flex-end;
}
[dir="ltr"] .orders-section .app-heading {
  text-align: left;
}
[dir="rtl"] .orders-section .app-heading {
  text-align: right;
}
[dir] .orders-section .table-wrapper {
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  margin: 0 0 -1px;
}
.orders-section .table-wrapper .table-body {
  overflow-x: auto;
}
[dir] .orders-section .table-wrapper .table-body {
  border-bottom: 1px solid #f0f0f0;
  margin: 0 0 -1px;
}
.orders-section .table-wrapper .table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
[dir] .orders-section .table-wrapper .table-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
}
[dir] .orders-section .table-wrapper .table-body::-webkit-scrollbar-track {
  background-color: #fafafa;
}
@media (max-width: 768px) {
  .orders-section .table-wrapper .table {
    width: 736px;
  }
}
[dir] .orders-section .table-wrapper .table td,
[dir] .orders-section .table-wrapper .table th {
  padding: 15px;
  text-align: center;
}
.orders-section .table-wrapper .table th {
  color: #78909c;
  font-weight: 600;
}
[dir] .orders-section .table-wrapper .table thead tr {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}
.orders-section .table-wrapper .table .table-ref {
  font-weight: 600;
}
.orders-section .table-wrapper .table .table-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 30px;
  line-height: 28px;
  font-size: 13px;
  font-weight: 600;
}
[dir] .orders-section .table-wrapper .table .table-label {
  text-align: center;
  padding: 0 15px;
  border-radius: 3px;
}
.orders-section .table-wrapper .table .label-1 {
  color: #00c853;
}
[dir] .orders-section .table-wrapper .table .label-1 {
  background-color: rgba(0, 200, 83, 0.2);
}
.orders-section .table-wrapper .table .label-2 {
  color: #8a9ca0;
}
[dir] .orders-section .table-wrapper .table .label-2 {
  background-color: #f2f6f7;
}
.orders-section .table-wrapper .table .label-3 {
  color: #fb323f;
}
[dir] .orders-section .table-wrapper .table .label-3 {
  background-color: rgba(251, 50, 63, 0.2);
}
.orders-section .table-wrapper .table .label-4,
.orders-section .table-wrapper .table .label-5 {
  color: #ffab00;
}
[dir] .orders-section .table-wrapper .table .label-4,
[dir] .orders-section .table-wrapper .table .label-5 {
  background-color: rgba(255, 171, 0, 0.2);
}
.orders-section .table-wrapper .table tbody tr {
  transition: background-color 0.25s;
}
[dir] .orders-section .table-wrapper .table tbody tr:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}
[dir] .orders-section .table-wrapper .table tbody tr:hover {
  background-color: #fafafa;
}
[dir] .orders-section .table-wrapper .table-footer {
  padding: 10px 15px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}
.orders-section .table-wrapper .pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
}
[dir] .orders-section .table-wrapper .pagination {
  padding: 0;
  margin: 0;
}
[dir="ltr"]
  .orders-section
  .table-wrapper
  .pagination
  .page-item:not(:last-child) {
  padding-right: 10px;
}
[dir="rtl"]
  .orders-section
  .table-wrapper
  .pagination
  .page-item:not(:last-child) {
  padding-left: 10px;
}
.orders-section .table-wrapper .pagination .page-item .page-link {
  width: 35px;
  height: 35px;
  line-height: 33px;
  display: block;
  transition: color 0.25s;
  color: inherit;
  opacity: 0.5;
}
[dir] .orders-section .table-wrapper .pagination .page-item .page-link {
  text-align: center;
  border: 1px solid transparent;
}
.orders-section .table-wrapper .pagination .page-item .page-link:hover {
  opacity: 1;
  color: var(--primary-color);
}
.orders-section .table-wrapper .pagination .page-item.active .page-link {
  opacity: 1;
  color: var(--primary-color);
  font-weight: 600;
}
[dir]
  .orders-section
  .table-wrapper
  .pagination
  .page-item.disabled
  .page-link {
  cursor: not-allowed;
}
.orders-section .table-wrapper .pagination .page-item:first-child .page-link,
.orders-section .table-wrapper .pagination .page-item:last-child .page-link {
  font-size: 0;
  line-height: 35px;
}
.orders-section
  .table-wrapper
  .pagination
  .page-item:first-child
  .page-link:before,
.orders-section
  .table-wrapper
  .pagination
  .page-item:last-child
  .page-link:before {
  font-family: icons;
  font-size: 14px;
}
.orders-section
  .table-wrapper
  .pagination
  .page-item:first-child
  .page-link:before {
  content: "\E904";
}
.orders-section
  .table-wrapper
  .pagination
  .page-item:last-child
  .page-link:before {
  content: "\E905";
}
.orders-section .order-message {
  max-width: 700px;
}
[dir] .orders-section .order-message {
  margin: 0 auto 30px;
}
.orders-section .order-message .message-header {
  width: 90px;
}
[dir] .orders-section .order-message .message-header {
  margin: 0 auto 20px;
}
[dir] .orders-section .order-message .message-body .app-heading {
  text-align: center;
  margin: 0;
}
[dir] .orders-section .order-message .message-body .fr-view {
  margin: 10px 0 0;
}
[dir] .orders-section .order-message .message-body .fr-view p,
[dir] .orders-section .order-message .message-body .fr-view ul {
  margin: 0;
}
[dir] .orders-section .order-message .message-body .fr-view p:not(:last-child) {
  margin: 0 0 10px;
}
[dir] .orders-section .order-message .message-body .actions {
  margin: 20px 0 0;
  text-align: center;
}
.orders-section .single-order {
  max-width: 700px;
}
[dir] .orders-section .single-order {
  margin: auto;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
  padding: 15px;
}
.orders-section .single-order .single-order-header {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}
[dir] .orders-section .single-order .single-order-header {
  margin: 0 0 15px;
}
.orders-section .single-order .single-order-header .order-item {
  width: 33.33333%;
  flex: 1 auto;
}
[dir] .orders-section .single-order .single-order-header .order-item {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  text-align: center;
  border-radius: 3px;
  padding: 12px;
}
.orders-section .single-order .single-order-header .order-item h2 {
  font-size: 14px;
  font-weight: 500;
  color: #a7a7a7;
}
[dir] .orders-section .single-order .single-order-header .order-item h2 {
  margin: 0 0 5px;
}
.orders-section .single-order .single-order-header .order-item p {
  font-size: 13px;
  font-weight: 600;
}
[dir] .orders-section .single-order .single-order-header .order-item p {
  margin: 0;
}
[dir="ltr"]
  .orders-section
  .single-order
  .single-order-header
  .order-item:not(:last-child) {
  margin: 0 10px 0 0;
}
[dir="rtl"]
  .orders-section
  .single-order
  .single-order-header
  .order-item:not(:last-child) {
  margin: 0 0 0 10px;
}
@media (max-width: 425px) {
  [dir]
    .orders-section
    .single-order
    .single-order-header
    .order-item:not(:last-child) {
    margin: 0 0 10px;
  }
}
@media (max-width: 425px) {
  .orders-section .single-order .single-order-header .order-item {
    width: 100%;
  }
}
@media (max-width: 425px) {
  .orders-section .single-order .single-order-header {
    flex-direction: column;
  }
}
.orders-section .single-order .single-order-body {
  overflow-x: auto;
}
[dir] .orders-section .single-order .single-order-body {
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
[dir] .orders-section .single-order .single-order-body .table td,
[dir] .orders-section .single-order .single-order-body .table th {
  padding: 15px;
}
[dir] .orders-section .single-order .single-order-body .table thead tr {
  border-bottom: 1px solid #f0f0f0;
}
.orders-section .single-order .single-order-body .table thead th {
  color: #a7a7a7;
  font-weight: 500;
}
.orders-section .single-order .single-order-body .table .table-ref a {
  display: block;
  max-width: 280px;
  font-weight: 600;
}
.orders-section .single-order .single-order-body .table .product-qty {
  display: block;
  width: 40px;
  height: 25px;
  font-size: 13px;
  font-weight: 500;
  line-height: 23px;
}
[dir] .orders-section .single-order .single-order-body .table .product-qty {
  margin: auto;
  padding: 0 3px;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.orders-section
  .single-order
  .single-order-body
  .table:not(.table-total)
  tbody
  tr {
  height: 50px;
}
[dir]
  .orders-section
  .single-order
  .single-order-body
  .table:not(.table-total)
  tbody
  tr {
  background-color: #fefefe;
  border-bottom: 1px solid #f0f0f0;
}
[dir]
  .orders-section
  .single-order
  .single-order-body
  .table:not(.table-total)
  tbody
  td {
  padding: 10px 15px;
}
[dir]
  .orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr
  td {
  padding: 0 20px 10px;
}
.orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr
  td:nth-child(3) {
  color: #a7a7a7;
  font-weight: 500;
}
[dir]
  .orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr:first-child
  td {
  padding: 15px 20px 10px;
}
.orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr:last-child
  td {
  color: #1a1a1a;
}
[dir]
  .orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr:last-child
  td {
  padding: 0 20px 15px;
}
.orders-section
  .single-order
  .single-order-body
  .table.table-total
  tr:last-child
  td
  .currency-value {
  font-weight: 600;
  color: var(--primary-color);
}
@media (max-width: 425px) {
  .orders-section .single-order .single-order-body .table {
    width: 600px;
  }
  .orders-section .single-order .single-order-body .table .mb-td {
    display: none;
  }
}
.orders-section .single-order .single-order-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
[dir]
  .orders-section
  .single-order
  .single-order-body::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
}
[dir]
  .orders-section
  .single-order
  .single-order-body::-webkit-scrollbar-track {
  background-color: #fafafa;
}
.orders-section .paid {
  color: #00c853;
}
[dir="ltr"] .orders-section .text-right {
  text-align: right;
}
[dir="rtl"] .orders-section .text-right {
  text-align: left;
}
[dir] .orders-section .text-center {
  text-align: center;
}
.orders-section .table-variants li {
  display: flex;
  color: #a7a7a7;
  font-weight: 400;
}
.orders-section .currency-value {
  font-weight: 500;
}
[dir="ltr"] .orders-section .currency-value .currency-value {
  margin: 0 0 0 5px;
}
[dir="rtl"] .orders-section .currency-value .currency-value {
  margin: 0 5px 0 0;
}
.upsell-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upsell-section .upsell-footer,
.upsell-section .upsell-header {
  word-break: break-word;
}
[dir] .upsell-section .upsell-footer p,
[dir] .upsell-section .upsell-header p {
  margin: 0;
}
[dir] .upsell-section .upsell-footer p:not(:last-child),
[dir] .upsell-section .upsell-header p:not(:last-child) {
  margin: 0 0 10px;
}
[dir] .upsell-section .upsell-body {
  margin: 20px 0;
}
.upsell-section .upsell-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.upsell-section .upsell-actions li {
  width: 100%;
}
[dir] .upsell-section .upsell-actions li {
  text-align: center;
}
[dir] .upsell-section .upsell-actions li:not(:last-child) {
  padding: 0 0 10px;
}
@media (max-width: 768px) {
  .upsell-section .upsell-actions li.no-btn-container .no-btn {
    height: 40px;
  }
}
.upsell-section .upsell-actions li .btn {
  width: 280px;
  height: 55px;
  color: #fff;
  text-transform: uppercase;
  font-weight: 600;
}
[dir] .upsell-section .upsell-actions li .btn {
  border-radius: 3px;
}
[dir] .upsell-section .upsell-actions li .btn.yes-btn {
  background-color: #37a510;
}
.upsell-section .upsell-actions li .btn.no-btn {
  color: #ec1d26;
}
[dir] .upsell-section .upsell-actions li .btn.no-btn {
  background-color: transparent;
}
.upsell-section .upsell-actions li .btn.font-small {
  font-size: 15px;
}
.upsell-section .upsell-actions li .btn.font-medium {
  font-size: 19px;
}
.upsell-section .upsell-actions li .btn.font-large {
  font-size: 22px;
}
.upsell-section .upsell-actions li .btn.size-small {
  width: 50%;
}
.upsell-section .upsell-actions li .btn.size-medium {
  width: 70%;
}
.upsell-section .upsell-actions li .btn.size-large {
  width: 100%;
}
@media (min-width: 768px) {
  .upsell-section .container {
    width: 750px;
  }
  [dir] .upsell-section .container {
    padding: 0;
  }
}
html {
  scroll-behavior: smooth;
}
[dir] .pages-section .app-heading {
  margin: 0 0 24px;
}
[dir="ltr"] .pages-section .app-heading {
  text-align: left;
}
[dir="rtl"] .pages-section .app-heading {
  text-align: right;
}
[dir] .pages-section .fr-view p {
  margin: 0;
}
[dir] .pages-section .fr-view p:not(:last-child) {
  margin: 0 0 15px;
}
@media (min-width: 768px) {
  .pages-section .contact-us {
    display: flex;
  }
}
@media (min-width: 768px) {
  .pages-section .contact-us .contact-form {
    width: 65%;
  }
  [dir="ltr"] .pages-section .contact-us .contact-form {
    padding: 0 30px 0 0;
  }
  [dir="rtl"] .pages-section .contact-us .contact-form {
    padding: 0 0 0 30px;
  }
}
@media (max-width: 768px) {
  [dir] .pages-section .contact-us .contact-form {
    padding: 0 0 30px;
  }
}
[dir] .pages-section .contact-us .contact-form .form-group:not(:last-child) {
  margin: 0 0 18px;
}
.pages-section .contact-us .contact-form .form-group .button {
  width: 100%;
}
.pages-section .contact-us .contact-form .has-icon {
  position: relative;
}
.pages-section .contact-us .contact-form .has-icon .yc {
  position: absolute;
  top: 15px;
  color: #ccc;
  font-size: 18px;
}
[dir="ltr"] .pages-section .contact-us .contact-form .has-icon .yc {
  right: 15px;
}
[dir="rtl"] .pages-section .contact-us .contact-form .has-icon .yc {
  left: 15px;
}
.pages-section .contact-us .contact-form .flex-form-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pages-section .contact-us .contact-form .captcha-code {
  display: block;
  height: 50px;
}
[dir] .pages-section .contact-us .contact-form .captcha-code {
  border-radius: 3px;
}
[dir="ltr"] .pages-section .contact-us .contact-form .captcha-code {
  margin: 0 15px 0 0;
}
[dir="rtl"] .pages-section .contact-us .contact-form .captcha-code {
  margin: 0 0 0 15px;
}
@media (max-width: 425px) {
  .pages-section .contact-us .contact-form .captcha-code {
    width: 100px;
  }
}
[dir] .pages-section .contact-us .contact-form .has-error input {
  border: 2px solid var(--danger-color);
}
.pages-section .contact-us .contact-form .validate-error {
  display: block;
  color: var(--danger-color);
}
[dir] .pages-section .contact-us .contact-form .validate-error {
  margin: 5px 0 0;
}
@media (min-width: 768px) {
  .pages-section .contact-us .page-body {
    width: 35%;
  }
}
.page-builder section {
  display: block;
}
.page-builder section.express-checkout-form-section .product-price-container {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}
[dir="ltr"]
  .page-builder
  section.express-checkout-form-section
  .product-price-container
  .product-compare-at-price {
  margin-left: 10px;
}
[dir="rtl"]
  .page-builder
  section.express-checkout-form-section
  .product-price-container
  .product-compare-at-price {
  margin-right: 10px;
}
.page-builder .color-picker {
  z-index: 99;
}
.page-builder .main-product {
  position: relative;
}
.page-builder .builder-products {
  display: flex;
  justify-content: space-between;
}
.page-builder .one-products-slider {
  justify-content: center;
}
.page-builder .two-products-slider .product-item {
  max-width: 50%;
  min-width: 47%;
}
.page-builder .three-products-slider .product-item {
  max-width: 33%;
  min-width: 30%;
}
.page-builder .featured-product {
  position: absolute;
  top: -5px;
  width: 200px;
  letter-spacing: 1px;
  word-break: break-word;
  word-wrap: break-word;
  z-index: 1;
  font-size: 17px;
  display: inline-block;
  line-height: 18px;
}
[dir] .page-builder .featured-product {
  text-align: center;
}
@media (max-width: 600px) {
  .page-builder .builder-products {
    display: block;
  }
  .page-builder .product-item {
    max-width: 100% !important;
  }
  [dir] .page-builder .product-item {
    margin: 15px 0;
  }
}
.page-builder .new-section-placeholder {
  width: 100%;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
[dir] .page-builder .new-section-placeholder {
  border: 2px solid #e0e0e0;
}
[dir="ltr"] .page-builder .new-section-placeholder {
  background: repeating-linear-gradient(
    45deg,
    #f0f0f0,
    #f0f0f0 5px,
    #fff 0,
    #fff 10px
  );
}
[dir="rtl"] .page-builder .new-section-placeholder {
  background: repeating-linear-gradient(
    -45deg,
    #f0f0f0,
    #f0f0f0 5px,
    #fff 0,
    #fff 10px
  );
}
.page-builder .new-section-placeholder i {
  font-size: 22px;
  font-weight: 700;
  color: #848484;
}
.page-builder .section-controls {
  position: absolute;
  width: 100%;
  height: 100%;
  display: none;
  top: 0;
  display: block;
  pointer-events: none;
}
[dir] .page-builder .section-controls {
  border: 2px solid #7aabc0;
}
[dir="ltr"] .page-builder .section-controls {
  left: 0;
}
[dir="rtl"] .page-builder .section-controls {
  right: 0;
}
.page-builder .section-controls > * {
  pointer-events: all;
}
.page-builder .section-controls .insert-section-after-action,
.page-builder .section-controls .insert-section-before-action {
  position: absolute;
  z-index: 10;
  top: -25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.1s ease-in;
  width: 90px;
  height: 24px;
  opacity: 0.7;
  z-index: 4;
}
[dir] .page-builder .section-controls .insert-section-after-action,
[dir] .page-builder .section-controls .insert-section-before-action {
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.17);
  background: #7aabc0;
}
[dir="ltr"] .page-builder .section-controls .insert-section-after-action,
[dir="ltr"] .page-builder .section-controls .insert-section-before-action {
  left: calc(50% - 45px);
}
[dir="rtl"] .page-builder .section-controls .insert-section-after-action,
[dir="rtl"] .page-builder .section-controls .insert-section-before-action {
  right: calc(50% - 45px);
}
.page-builder .section-controls .insert-section-after-action i,
.page-builder .section-controls .insert-section-before-action i {
  font-size: 19px;
  font-weight: 700;
  color: #fff;
}
[dir] .page-builder .section-controls .insert-section-after-action i,
[dir] .page-builder .section-controls .insert-section-before-action i {
  margin: auto;
}
.page-builder .section-controls .insert-section-after-action:hover,
.page-builder .section-controls .insert-section-before-action:hover {
  opacity: 1;
}
[dir] .page-builder .section-controls .insert-section-after-action:hover,
[dir] .page-builder .section-controls .insert-section-before-action:hover {
  transform: scale(1.05);
}
.page-builder .section-controls .insert-section-after-action {
  bottom: -25px;
  top: unset;
}
.page-builder .section-controls .section-actions {
  z-index: 30;
  position: absolute;
  display: grid;
  gap: 3px;
  grid-template-columns: repeat(3, 1fr);
  bottom: -36px;
}
[dir] .page-builder .section-controls .section-actions {
  padding: 3px;
}
[dir="ltr"] .page-builder .section-controls .section-actions {
  right: 3px;
}
[dir="rtl"] .page-builder .section-controls .section-actions {
  left: 3px;
}
[dir="ltr"] .inner-column .section-actions {
  left: 3px;
  right: unset !important;
}
[dir="rtl"] .inner-column .section-actions {
  right: 3px;
  left: unset !important;
}
.page-builder.preview [data-pb-field] {
  position: relative;
}
.page-builder.preview [data-pb-field]:after {
  display: none;
  position: absolute;
  font-family: icons;
  color: #000;
  content: "\E908";
  font-size: 15px;
  line-height: 1.4;
  width: 40px;
  height: 20px;
  bottom: 0;
  top: unset;
}
[dir] .page-builder.preview [data-pb-field]:after {
  background-color: #7aabc0;
  padding: 0;
}
[dir="ltr"] .page-builder.preview [data-pb-field]:after {
  left: 50%;
  transform: translateX(-50%);
  border-top-left-radius: 99999px;
  border-top-right-radius: 99999px;
}
[dir="rtl"] .page-builder.preview [data-pb-field]:after {
  right: 50%;
  transform: translateX(50%);
  border-top-right-radius: 99999px;
  border-top-left-radius: 99999px;
}
.page-builder.preview [data-pb-field]:hover:after {
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-builder.preview [data-pb-field]:hover:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  outline: 1px solid #7aabc0;
  z-index: 100;
}
[dir] .page-builder.preview [data-pb-field]:hover:before {
  border-radius: inherit;
}
[dir="ltr"] .page-builder.preview [data-pb-field]:hover:before {
  left: 0;
}
[dir="rtl"] .page-builder.preview [data-pb-field]:hover:before {
  right: 0;
}
.page-builder.preview img[data-pb-field]:hover {
  outline: 1px solid #7aabc0;
}
[dir] .page-builder.preview img[data-pb-field]:hover {
  border-radius: inherit;
}
.builder-preview-delete,
.builder-preview-duplicate,
.builder-preview-edit {
  justify-content: space-between;
  align-items: center;
  display: flex;
  justify-content: space-around;
}
[dir] .builder-preview-delete,
[dir] .builder-preview-duplicate,
[dir] .builder-preview-edit {
  cursor: pointer;
  border-radius: 2px;
  background: #7aabc0;
  padding: 6px;
}
.builder-preview-delete i,
.builder-preview-delete svg,
.builder-preview-duplicate i,
.builder-preview-duplicate svg,
.builder-preview-edit i,
.builder-preview-edit svg {
  font-size: 19px;
  font-weight: 700;
  color: #fff;
}
[dir] .builder-preview-delete:hover,
[dir] .builder-preview-duplicate:hover,
[dir] .builder-preview-edit:hover {
  background: #396679;
}
.page-builder-express-checkout-wrapper
  .checkout-groups
  .form-group
  .form-label {
  display: none;
}
[dir] .page-builder-express-checkout-wrapper .ec-form-footer {
  margin-top: 14px;
}
[dir] .page-builder-express-checkout-wrapper .ec-form-footer p {
  margin: 0;
}
[dir="rtl"] .page-builder .single-countdown .duration:not(:last-child):after {
  left: unset !important;
  right: 0 !important;
}
section.debug-mode-enabled.column-section > .inner-container > div,
section.debug-mode-enabled.row-section > .inner-container > div {
  outline: 2px solid #000;
  outline-offset: -1px;
}
section.debug-mode-enabled.column-section > .inner-container > .inner-column,
section.debug-mode-enabled.row-section > .inner-container > .inner-column {
  outline-color: #fda4af;
}
[dir]
  section.debug-mode-enabled.column-section
  > .inner-container
  > .inner-column,
[dir]
  section.debug-mode-enabled.row-section
  > .inner-container
  > .inner-column {
  box-shadow: 0 0 0 4px #fff1f2;
}
section.debug-mode-enabled.column-section > .inner-container > .inner-row,
section.debug-mode-enabled.row-section > .inner-container > .inner-row {
  outline-color: #93c5fd;
}
[dir] section.debug-mode-enabled.column-section > .inner-container > .inner-row,
[dir] section.debug-mode-enabled.row-section > .inner-container > .inner-row {
  box-shadow: 0 0 0 4px #eff6ff;
}
.accordion-list .accordion-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .accordion-list .accordion-list-header {
  border-radius: 3px;
  cursor: pointer;
}
.express-checkout-invalid {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  color: #a73a36;
  font-size: 16px;
  font-weight: 500;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
[dir] .express-checkout-invalid {
  padding: 24px 56px;
  border-radius: 20px;
  background: #ffcaca;
  border: 2px solid #a73a36;
  margin: 10px auto;
  cursor: not-allowed;
}
.errors-section {
  font-family: Lato, sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}
[dir] .errors-section {
  text-align: center;
}
[dir] .errors-section .errors-body p {
  margin: 10px 0 0;
}
.errors-section .errors-body a {
  color: #752651;
}
.checkout-header {
  position: sticky;
  top: 0;
  height: 100px;
  z-index: 1;
}
[dir] .checkout-header {
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 5px 20px -10px rgba(0, 0, 0, 0.1);
}
.checkout-header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}
@media (max-width: 425px) {
  .checkout-header .container {
    justify-content: center;
  }
}
.checkout-header .checkout-brand {
  display: flex;
  align-items: center;
  max-width: 170px;
  height: 85px;
}
@media (max-width: 1124px) {
  .checkout-header .checkout-brand {
    max-width: 120px;
    height: 55px;
  }
}
.checkout-header .checkout-brand img {
  max-height: 100%;
}
@media (max-width: 1124px) {
  .checkout-header {
    height: 60px;
  }
}
@media (max-width: 425px) {
  .checkout-header .button {
    display: none;
  }
}
[dir] .checkout-section {
  padding: 30px 0;
}
.checkout-section .checkout {
  display: flex;
  flex-wrap: wrap;
  flex: 1 0 auto;
}
@media (min-width: 768px) {
  .checkout-section .checkout {
    align-items: flex-start;
    justify-content: space-between;
  }
}
@media (max-width: 768px) {
  .checkout-section .checkout {
    justify-content: center;
    flex-direction: column;
  }
}
.checkout-section .checkout .main,
.checkout-section .checkout .sidebar {
  flex: 1 0 auto;
}
[dir] .checkout-section .checkout .checkout-loading,
[dir] .checkout-section .checkout input[type="email"],
[dir] .checkout-section .checkout input[type="number"],
[dir] .checkout-section .checkout input[type="password"],
[dir] .checkout-section .checkout input[type="tel"],
[dir] .checkout-section .checkout input[type="text"],
[dir] .checkout-section .checkout select,
[dir] .checkout-section .checkout textarea {
  border-radius: 5px;
}
.checkout-section .checkout .checkout-loading {
  height: 50px;
}
[dir] .checkout-section .checkout .has-error input,
[dir] .checkout-section .checkout .has-error textarea {
  border: 2px solid var(--danger-color);
}
.checkout-section .checkout .has-error .validate-error {
  display: block;
  color: var(--danger-color);
}
[dir] .checkout-section .checkout .has-error .validate-error {
  margin: 5px 0 0;
}
.checkout-section .checkout .radio-groups {
  width: 100%;
}
[dir] .checkout-section .checkout .radio-groups {
  padding: 0 7.5px;
}
.checkout-section .checkout .radio-group {
  display: flex;
  width: 100%;
}
[dir] .checkout-section .checkout .radio-group {
  padding: 12px 15px;
  margin: 0 0 15px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
}
.checkout-section .checkout .radio-group .radio {
  flex: 1 auto;
}
.checkout-section .checkout .radio-group .radio label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  justify-content: space-between;
}
.checkout-section .checkout .radio-group .radio label .currency-value {
  font-weight: 500;
}
.checkout-section .checkout .main .checkout-form .checkout-heading {
  width: 100%;
  font-size: 15px;
  font-weight: 500;
}
[dir] .checkout-section .checkout .main .checkout-form .checkout-heading {
  margin: 0 0 10px;
  padding: 0 7.5px;
}
.checkout-section .checkout .main .checkout-form .checkout-select {
  position: relative;
}
.checkout-section .checkout .main .checkout-form .checkout-select .form-label {
  display: block;
  line-height: 1;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-select
  .form-label {
  margin: 0 0 6px;
}
.checkout-section .checkout .main .checkout-form .checkout-select select {
  width: 100%;
  height: 50px;
}
[dir] .checkout-section .checkout .main .checkout-form .checkout-select select {
  border: 1px solid #e5e5e5;
  background-color: transparent;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-select
  select {
  padding: 9px 45px 10px 15px;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-select
  select {
  padding: 9px 15px 10px 45px;
}
.checkout-section .checkout .main .checkout-form .checkout-select:before {
  font-family: icons;
  content: "\E903";
  position: absolute;
  bottom: 14px;
  font-size: 15px;
  color: #737373;
  pointer-events: none;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-select:before {
  right: 15px;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-select:before {
  left: 15px;
}
.checkout-section .checkout .main .checkout-form .checkout-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir] .checkout-section .checkout .main .checkout-form .checkout-loading {
  border: 1px solid #e5e5e5;
}
.checkout-section .checkout .main .checkout-form .checkbox {
  display: block;
}
.checkout-section .checkout .main .checkout-form .checkout-groups {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
}
[dir] .checkout-section .checkout .main .checkout-form .checkout-groups {
  margin: 0 -7.5px;
}
.checkout-section .checkout .main .checkout-form .checkout-groups .form-group {
  width: 50%;
  flex: 1 auto;
  flex-basis: 50%;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group {
  margin: 0 0 15px;
  padding: 0 7.5px;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group.wide-group {
  width: 100%;
  flex-basis: 100%;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  textarea {
  display: block;
  min-height: 100px;
}
@media (max-width: 425px) {
  .checkout-section
    .checkout
    .main
    .checkout-form
    .checkout-groups
    .form-group {
    width: 100%;
    flex-basis: 100%;
  }
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  .form-label {
  display: block;
  width: 100%;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  .form-label {
  margin-bottom: 6px;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  .form-label {
  text-align: left;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  .form-label {
  text-align: right;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .form-group
  > .form-label {
  display: none;
}
.checkout-section .checkout .main .checkout-form .checkout-groups .grid-2,
.checkout-section .checkout .main .checkout-form .checkout-groups .grid-3 {
  display: grid;
}
.checkout-section .checkout .main .checkout-form .checkout-groups .grid-2,
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .grid-2
  .form-group,
.checkout-section .checkout .main .checkout-form .checkout-groups .grid-3,
.checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .grid-3
  .form-group {
  width: 100%;
}
@media (max-width: 425px) {
  .checkout-section .checkout .main .checkout-form .checkout-groups .grid-2,
  .checkout-section .checkout .main .checkout-form .checkout-groups .grid-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups:not(:last-child) {
  margin-bottom: 8px;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .checkout-groups
  .checkout-groups {
  margin: 0;
}
[dir] .checkout-section .checkout .main .checkout-form .customer-infos {
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  margin: 0 0 24px;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-header {
  padding: 12px 15px;
  background-color: #fcfcfc;
  border-radius: 5px;
  cursor: pointer;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-header
  .yc,
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-header
  h2 {
  font-size: 15px;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-header
  h2 {
  font-weight: 500;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body {
  display: none;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  margin: -1px 0 0;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li {
  background-color: #fcfcfc;
  padding: 10px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  text-align: center;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li
  span {
  display: block;
  color: #525252;
  font-weight: 500;
  text-transform: lowercase;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li
  span {
  margin: 0 0 5px;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li
  span:first-letter {
  text-transform: capitalize;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li
  p {
  margin: 0;
}
[dir]
  .checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body
  li:not(:last-child) {
  margin: 0 0 10px;
}
.checkout-section
  .checkout
  .main
  .checkout-form
  .customer-infos
  .customer-body.active {
  display: block;
}
.checkout-section .checkout .main .checkout-form.is-disabled {
  opacity: 0.5;
  pointer-events: none;
}
[dir] .checkout-section .checkout .main .shipping-methods {
  background-color: #fcfcfc;
  padding: 15px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  margin: 0 0 15px;
}
[dir] .checkout-section .checkout .main .shipping-methods .radio-group {
  background-color: #fff;
  margin: 0;
}
[dir]
  .checkout-section
  .checkout
  .main
  .shipping-methods
  .radio-group:not(:last-child) {
  margin: 0 0 10px;
}
[dir] .checkout-section .checkout .main .payment-gateways .payment-gateway {
  border-radius: 5px;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway:not(:last-child) {
  margin: 0 0 15px;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-header {
  background-color: #fcfcfc;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  padding: 12px 15px;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-header
  .radio {
  flex: 1 auto;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-header
  .payment-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 5px;
  width: 30%;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body {
  display: none;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body {
  border: 1px solid #e5e5e5;
  padding: 15px;
  border-radius: 0 0 5px 5px;
  margin: -1px 0 0;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid {
  display: grid;
  grid-gap: 10px;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid
  .stripe-field {
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  padding: 13px 15px 12px;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid
  .stripe-field
  input {
  width: 100%;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid
  .stripe-field
  input {
  border: 0;
  padding: 0;
  background-color: transparent;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid
  .stripe-field.card-holder {
  padding: 11px 15px;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .stripe-grid
  .stripe-field.StripeElement--invalid {
  border: 2px solid var(--danger-color);
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .validate-error {
  display: block;
  color: var(--danger-color);
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .validate-error {
  margin: 5px 0 0;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .paypal-button {
  width: 100%;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .paypal-button {
  background-color: #fcfcfc;
  border-radius: 5px;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway
  .payment-gateway-body
  .paypal-button
  img {
  width: 230px;
  height: 70px;
}
[dir]
  .checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway.expanded
  .payment-gateway-header {
  border-radius: 5px 5px 0 0;
}
.checkout-section
  .checkout
  .main
  .payment-gateways
  .payment-gateway.expanded
  .payment-gateway-body {
  display: block;
}
.checkout-section .checkout .main .checkout-actions {
  display: grid;
  grid-gap: 10px;
}
[dir] .checkout-section .checkout .main .checkout-actions {
  margin: 8px 0 0;
}
[dir] .checkout-section .checkout .main .checkout-actions .button {
  border-radius: 5px;
  padding: 11px 24px 12px;
}
@media (min-width: 425px) {
  .checkout-section .checkout .main .checkout-actions .button.mobile-button {
    display: none;
  }
}
@media (max-width: 425px) {
  [dir]
    .checkout-section
    .checkout
    .main
    .checkout-actions
    .button.mobile-button {
    background-color: transparent;
    border: 0;
    padding: 0;
  }
}
@media (min-width: 768px) {
  .checkout-section .checkout .main {
    width: 60%;
  }
  [dir="ltr"] .checkout-section .checkout .main {
    padding: 0 30px 0 0;
  }
  [dir="rtl"] .checkout-section .checkout .main {
    padding: 0 0 0 30px;
  }
}
@media (max-width: 768px) {
  .checkout-section .checkout .main {
    width: 100%;
  }
  [dir] .checkout-section .checkout .main {
    padding: 0 0 20px;
  }
}
[dir] .checkout-section .checkout .sidebar {
  border: 1px solid #e5e5e5;
  border-radius: 5px;
}
@media (min-width: 768px) {
  .checkout-section .checkout .sidebar {
    position: sticky;
    width: 40%;
  }
}
@media (min-width: 1124px) {
  .checkout-section .checkout .sidebar {
    top: 130px;
  }
}
@media (min-width: 768px) and (max-width: 1124px) {
  .checkout-section .checkout .sidebar {
    top: 90px;
  }
}
.checkout-section .checkout .sidebar .aside-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
[dir] .checkout-section .checkout .sidebar .aside-header {
  padding: 12px 15px;
  background-color: #fcfcfc;
  border-radius: 5px;
  cursor: pointer;
}
@media (min-width: 768px) {
  .checkout-section .checkout .sidebar .aside-header {
    display: none;
  }
}
[dir] .checkout-section .checkout .sidebar .aside-header.active {
  border-bottom: 1px solid #e5e5e5;
  border-radius: 5px 5px 0 0;
}
.checkout-section .checkout .sidebar .aside-header h2 {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 500;
}
.checkout-section .checkout .sidebar .aside-header h2 .yc {
  font-size: 18px;
}
[dir="ltr"] .checkout-section .checkout .sidebar .aside-header h2 .yc {
  margin: 0 10px 0 0;
}
[dir="rtl"] .checkout-section .checkout .sidebar .aside-header h2 .yc {
  margin: 0 0 0 10px;
}
.checkout-section .checkout .sidebar .aside-header .yc,
.checkout-section .checkout .sidebar .aside-header h2 {
  font-size: 15px;
}
[dir] .checkout-section .checkout .sidebar .aside-body {
  background-color: #fcfcfc;
  padding: 30px;
  border-radius: 5px;
}
@media (max-width: 768px) {
  .checkout-section .checkout .sidebar .aside-body {
    display: none;
  }
  [dir] .checkout-section .checkout .sidebar .aside-body {
    padding: 15px;
    background-color: #fff;
  }
  .checkout-section .checkout .sidebar .aside-body.active {
    display: block;
  }
}
[dir] .checkout-section .checkout .sidebar .aside-body .coupon-item {
  background-color: #fff;
  padding: 15px;
  margin: 0 0 25px;
  border-radius: 5px;
  border: 1px solid #e5e5e5;
}
.checkout-section .checkout .sidebar .aside-body .coupon-item .form-label {
  display: block;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .form-label {
  margin: 0 0 6px;
}
.checkout-section .checkout .sidebar .aside-body .coupon-item .coupon-actions {
  position: relative;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  input {
  height: 40px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button {
  position: absolute;
  top: 0;
  height: 100%;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button {
  padding: 8px 12px 9px;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button {
  right: 0;
  border-radius: 0 3px 3px 0;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button {
  left: 0;
  border-radius: 3px 0 0 3px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button:hover {
  color: #fff;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button:hover {
  background-color: var(--primary-color);
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-actions
  .button
  .yc {
  display: block;
  font-size: 16px;
}
.checkout-section .checkout .sidebar .aside-body .coupon-item .coupon-message {
  color: var(--primary-color);
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .coupon-item
  .coupon-message {
  margin: 5px 0 0;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}
.checkout-section .checkout .sidebar .aside-body .currency-value {
  color: #313131;
  font-weight: 500;
}
.checkout-section .checkout .sidebar .aside-body .aside-products li {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li:not(:last-child) {
  padding: 0 0 20px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail {
  display: flex;
  align-content: center;
  justify-content: center;
  position: relative;
  width: 65px;
  height: 65px;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail {
  border-radius: 5px;
  border: 1px solid #e5e5e5;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail {
  margin: 0 15px 0 0;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail {
  margin: 0 0 0 15px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  img {
  max-height: 100%;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  img {
  border-radius: 5px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  .product-quantity {
  position: absolute;
  top: -12px;
  min-width: 24px;
  height: 24px;
  font-size: 12px;
  font-weight: 500;
  line-height: 24px;
  color: #fff;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  .product-quantity {
  border-radius: 50%;
  background-color: var(--primary-color);
  text-align: center;
}
[dir="ltr"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  .product-quantity {
  right: -12px;
}
[dir="rtl"]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-thumbnail
  .product-quantity {
  left: -12px;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: calc(100% - 80px);
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-info {
  padding: 12px 0 0;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-info
  .product-title {
  display: block;
  color: inherit;
  font-weight: 500;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-info
  .product-title
  span {
  display: block;
  color: #6f6f6f;
  font-size: 13px;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-products
  li
  .product-info
  .product-title
  span {
  margin: 2px 0 0;
}
[dir] .checkout-section .checkout .sidebar .aside-body .aside-total-details {
  margin: 15px 0;
  padding: 15px 0;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}
.checkout-section .checkout .sidebar .aside-body .aside-total-details li {
  display: flex;
  align-content: center;
  justify-content: space-between;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-total-details
  li
  .title {
  color: #525252;
  font-weight: 400;
}
[dir]
  .checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-total-details
  li:not(:last-child) {
  margin: 0 0 8px;
}
.checkout-section .checkout .sidebar .aside-body .aside-total h4 {
  display: flex;
  align-content: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 500;
}
.checkout-section
  .checkout
  .sidebar
  .aside-body
  .aside-total
  h4
  .currency-value {
  color: var(--primary-color);
}
.checkout-section .checkout .sidebar .sidebar {
  width: 100%;
}
[dir] .checkout-section .app-heading {
  margin: 0 0 24px;
}
[dir="ltr"] .checkout-section .app-heading {
  text-align: left;
}
[dir="rtl"] .checkout-section .app-heading {
  text-align: right;
}
.checkout-section .empty-gateways,
.checkout-section .shipping-message {
  width: 100%;
}
[dir] .checkout-section .empty-gateways,
[dir] .checkout-section .shipping-message {
  padding: 20px 15px;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  background-color: #fcfcfc;
  text-align: center;
}
.checkout-section .empty-gateways h3,
.checkout-section .shipping-message h3 {
  font-size: 15px;
  font-weight: 500;
}
@media (max-width: 768px) {
  .checkout-section .all-in-one .checkout {
    flex-direction: column;
  }
}
.checkout-section .all-in-one .main {
  display: grid;
  grid-gap: 20px;
}
.checkout-section .all-in-one .main .ycpayment-form-container {
  max-width: 580px;
}
@media (max-width: 768px) {
  [dir] .checkout-section .all-in-one .main {
    padding: 0 0 20px;
  }
  .checkout-section .all-in-one .main .ycpayment-form-container {
    max-width: calc(100vw - 94px);
  }
}
[dir] .checkout-section .all-in-one .sidebar {
  border: 0;
  border-radius: 0;
}
[dir] .checkout-section .all-in-one .sidebar .aside-body {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
}
[dir] .checkout-section .all-in-one .all-in-one-step {
  border: 1px solid #e5e5e5;
  border-radius: 5px;
}
[dir] .checkout-section .all-in-one .all-in-one-step .all-in-one-header {
  background-color: #fcfcfc;
  border-radius: 5px;
  cursor: pointer;
}
.checkout-section .all-in-one .all-in-one-step .all-in-one-header h5 {
  display: flex;
  align-items: center;
}
.checkout-section
  .all-in-one
  .all-in-one-step
  .all-in-one-header
  h5
  .step-number {
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 18px;
  color: #c6c6c6;
}
[dir]
  .checkout-section
  .all-in-one
  .all-in-one-step
  .all-in-one-header
  h5
  .step-number {
  text-align: center;
  background-color: #f1f1f1;
}
[dir="ltr"]
  .checkout-section
  .all-in-one
  .all-in-one-step
  .all-in-one-header
  h5
  .step-number {
  border-radius: 5px 0 0 0;
  margin: 0 15px 0 0;
  border-right: 1px solid #e5e5e5;
}
[dir="rtl"]
  .checkout-section
  .all-in-one
  .all-in-one-step
  .all-in-one-header
  h5
  .step-number {
  border-radius: 0 5px 0 0;
  margin: 0 0 0 15px;
  border-left: 1px solid #e5e5e5;
}
.checkout-section
  .all-in-one
  .all-in-one-step
  .all-in-one-header
  h5
  .step-title {
  font-size: 16px;
  font-weight: 500;
}
.checkout-section .all-in-one .all-in-one-step .all-in-one-body {
  display: none;
}
[dir] .checkout-section .all-in-one .all-in-one-step .all-in-one-body {
  border-radius: 0 0 5px 5px;
  margin: -1px 0 0;
  padding: 15px;
}
.checkout-section .all-in-one .all-in-one-step .all-in-one-body.is-disabled {
  opacity: 0.5;
  pointer-events: none;
}
[dir]
  .checkout-section
  .all-in-one
  .all-in-one-step.expanded
  .all-in-one-header {
  border-radius: 5px 5px 0 0;
  border-bottom: 1px solid #e5e5e5;
}
.checkout-section .all-in-one .all-in-one-step.expanded .all-in-one-body {
  display: block;
}
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: 500;
  outline: none;
  line-height: inherit;
  transition: all 0.25s;
}
[dir] .button {
  border: 1px solid transparent;
  border-radius: 3px;
  padding: 8px 24px 9px;
  cursor: pointer;
}
[dir] .primary-button {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
.primary-button:hover {
  color: var(--primary-color);
}
[dir] .primary-button:hover {
  background-color: transparent;
}
.secondary-button {
  color: var(--primary-color);
}
[dir] .secondary-button {
  border-color: var(--primary-color);
}
.secondary-button:hover {
  color: #fff;
}
[dir] .secondary-button:hover {
  background-color: var(--primary-color);
}
.default-button {
  color: #878787;
}
[dir] .default-button {
  background-color: #fff;
  border-color: #e5e5e5;
}
.default-button:hover {
  color: #787878;
}
[dir] .small-button {
  padding: 6px 24px 7px;
}
@media screen and (min-width: 784px) {
  .sticky-desktop {
    position: fixed !important;
    bottom: 0;
    z-index: 2;
  }
  [dir="ltr"] .sticky-desktop {
    left: 0;
  }
  [dir="rtl"] .sticky-desktop {
    right: 0;
  }
}
@media screen and (max-width: 784px) {
  .sticky-mobile {
    position: fixed !important;
    bottom: 0;
    z-index: 2;
  }
  [dir="ltr"] .sticky-mobile {
    left: 0;
  }
  [dir="rtl"] .sticky-mobile {
    right: 0;
  }
}
.checkbox,
.radio {
  display: inline-block;
  position: relative;
  min-width: 22px;
  min-height: 22px;
}
.checkbox input,
.radio input {
  position: absolute;
}
[dir="ltr"] .checkbox input,
[dir="ltr"] .radio input {
  left: -9999px;
}
[dir="rtl"] .checkbox input,
[dir="rtl"] .radio input {
  right: -9999px;
}
.checkbox input:disabled + label,
.radio input:disabled + label {
  pointer-events: none;
  opacity: 0.6;
}
[dir] .checkbox input:disabled + label,
[dir] .radio input:disabled + label {
  cursor: not-allowed;
}
.checkbox label,
.radio label {
  display: inline-flex;
  align-items: flex-start;
  position: absolute;
  top: 1px;
  width: 100%;
  height: 100%;
  color: #7b7b7b;
}
[dir="ltr"] .checkbox label,
[dir="ltr"] .radio label {
  left: 0;
}
[dir="rtl"] .checkbox label,
[dir="rtl"] .radio label {
  right: 0;
}
.checkbox label:after,
.checkbox label:before,
.radio label:after,
.radio label:before {
  content: "";
  position: absolute;
  transition: all 0.25s;
}
.checkbox label:before,
.radio label:before {
  top: 0;
  width: 16px;
  height: 16px;
}
[dir] .checkbox label:before,
[dir] .radio label:before {
  background-color: #fff;
  border: 2px solid #e5e5e5;
}
[dir="ltr"] .checkbox label:before,
[dir="ltr"] .radio label:before {
  left: 0;
}
[dir="rtl"] .checkbox label:before,
[dir="rtl"] .radio label:before {
  right: 0;
}
[dir="ltr"] .checkbox.has-text label,
[dir="ltr"] .radio.has-text label {
  padding: 0 0 0 30px;
}
[dir="rtl"] .checkbox.has-text label,
[dir="rtl"] .radio.has-text label {
  padding: 0 30px 0 0;
}
.checkbox input:checked + label,
.checkbox input:disabled + label {
  color: var(--primary-color);
}
[dir] .checkbox input:checked + label:before,
[dir] .checkbox input:disabled + label:before {
  border-color: var(--primary-color);
}
[dir="ltr"] .checkbox input:checked + label:after,
[dir="ltr"] .checkbox input:disabled + label:after {
  transform: rotate(-45deg) scale(1);
}
[dir="rtl"] .checkbox input:checked + label:after,
[dir="rtl"] .checkbox input:disabled + label:after {
  transform: rotate(45deg) scale(1);
}
[dir] .checkbox label:after,
[dir] .checkbox label:before {
  border-radius: 3px;
}
.checkbox label:after {
  top: 7px;
  width: 6px;
  height: 3px;
}
[dir] .checkbox label:after {
  border-style: solid;
  border-color: var(--primary-color);
}
[dir="ltr"] .checkbox label:after {
  left: 6px;
  border-width: 0 0 2px 2px;
  transform: rotate(-45deg) scale(0);
}
[dir="rtl"] .checkbox label:after {
  right: 6px;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg) scale(0);
}
.radio input {
  position: absolute;
}
[dir="ltr"] .radio input {
  left: -9999px;
}
[dir="rtl"] .radio input {
  right: -9999px;
}
.radio input:checked + label,
.radio input:disabled + label {
  color: var(--primary-color);
}
[dir] .radio input:checked + label:before,
[dir] .radio input:disabled + label:before {
  border-color: var(--primary-color);
}
[dir] .radio input:checked + label:after,
[dir] .radio input:disabled + label:after {
  transform: scale(1);
}
[dir] .radio label:after,
[dir] .radio label:before {
  border-radius: 50%;
}
.radio label:after {
  top: 5px;
  width: 10px;
  height: 10px;
}
[dir] .radio label:after {
  background-color: var(--primary-color);
  transform: scale(0);
}
[dir="ltr"] .radio label:after {
  left: 5px;
}
[dir="rtl"] .radio label:after {
  right: 5px;
}
[dir] .footer {
  background-color: #fefefe;
  border-top: 1px solid #f0f0f0;
}
[dir] .footer .footer-body,
[dir] .footer .footer-brand,
[dir] .footer .footer-social {
  padding: 30px 0;
}
@media (max-width: 425px) {
  [dir] .footer .footer-body,
  [dir] .footer .footer-brand,
  [dir] .footer .footer-social {
    padding: 20px 0;
  }
}
.footer .footer-brand a {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 170px;
  height: 85px;
}
[dir] .footer .footer-brand a {
  margin: auto;
}
.footer .footer-brand a img {
  max-height: 100%;
}
@media (max-width: 1124px) {
  .footer .footer-brand a {
    max-width: 120px;
    height: 55px;
  }
}
[dir] .footer .footer-body {
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}
.footer .footer-item h3 {
  font-size: 15px;
  font-weight: 600;
}
[dir] .footer .footer-item h3 {
  padding: 0 0 20px;
}
.footer .footer-item h3:after {
  content: "";
  display: block;
  width: 25px;
  height: 1px;
}
[dir] .footer .footer-item h3:after {
  background-color: var(--primary-color);
  margin: 8px 0 0;
}
@media (max-width: 768px) {
  [dir] .footer .footer-item h3:after {
    margin: 8px auto 0;
  }
}
@media (max-width: 768px) {
  [dir] .footer .footer-item {
    text-align: center;
  }
}
[dir] .footer .footer-list li:not(:last-child) {
  margin: 0 0 10px;
}
.footer .footer-list li a {
  display: block;
  font-size: 13px;
  transition: color 0.25s;
}
.footer .footer-list li a:hover {
  color: var(--primary-color) !important;
}
.footer .footer-social {
  display: flex;
  align-items: center;
  justify-content: center;
}
[dir="ltr"] .footer .footer-social li:not(:last-child) {
  margin-right: 10px;
}
[dir="rtl"] .footer .footer-social li:not(:last-child) {
  margin-left: 10px;
}
.footer .footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  font-size: 20px;
  line-height: 45px;
  color: #fff;
}
[dir] .footer .footer-social a {
  text-align: center;
  border-radius: 50%;
}
[dir] .footer .footer-social .facebook {
  background-color: #4267b1;
}
[dir] .footer .footer-social .twitter {
  background-color: #4cb3f4;
}
[dir] .footer .footer-social .instagram {
  background-color: #e03c66;
}
[dir] .footer .footer-social .tiktok {
  background-color: #000;
}
@media (max-width: 768px) {
  .footer .grid-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
[dir="rtl"] .breadcrumb .breadcrumb-list li:not(:last-child):after,
[dir="rtl"] .products-slider .slick-next:before,
[dir="rtl"] .side-navigation .navigation-list li a:after,
[dir="rtl"] .single-product .thumbnails .slick-next:before {
  content: "\E904";
}
[dir="rtl"] .products-slider .slick-prev:before,
[dir="rtl"] .single-product .thumbnails .slick-prev:before {
  content: "\E905";
}
[dir="rtl"] .slider-container .slick-arrow.slick-next:before {
  content: "\F104";
}
[dir="rtl"] .slider-container .slick-arrow.slick-prev:before {
  content: "\F105";
}
[dir="rtl"] .single-product .single-hurry {
  letter-spacing: 0;
}
[dir="rtl"] .order-item h2 {
  font-weight: 600;
}
[dir="rtl"] .order-item p {
  direction: ltr;
}
.preview-form-container {
  max-width: 420px;
}
[dir] .preview-form-container {
  margin: auto;
}
.preview-form-container.active form {
  display: block;
}
[dir] .preview-form-container.active .preview-header .inner-header-container,
[dir] .preview-form-container.active form {
  border-color: var(--primary-color);
}
.preview-form-container.active
  .preview-header
  .inner-header-container
  .form-label {
  color: var(--primary-color);
}
.preview-form-container form {
  display: none;
}
[dir] .preview-form-container form {
  padding: 15px;
  margin: 15px 0;
  border-radius: 5px;
  border: 1px solid #e5e5e5;
  background: #fcfcfc;
}
.preview-form-container .form-group .form-label {
  color: #4a4a4a;
}
[dir] .preview-form-container .form-group .form-label {
  margin: 0 0 6px;
  text-align: start;
}
.preview-form-container .form-group .form-container {
  display: grid;
  grid-template-columns: 3fr 1fr;
}
.preview-form-container .form-group .form-container input {
  height: 40px;
}
[dir] .preview-form-container .form-group .form-container input {
  border: 1px solid #e5e5e5;
  padding: 10px 15px;
  background-color: transparent;
}
[dir="ltr"] .preview-form-container .form-group .form-container input {
  border-radius: 3px 0 0 3px;
}
[dir="ltr"] .preview-form-container .form-group .form-container input button,
[dir="rtl"] .preview-form-container .form-group .form-container input {
  border-radius: 0 3px 3px 0;
}
[dir="rtl"] .preview-form-container .form-group .form-container input button {
  border-radius: 3px 0 0 3px;
}
[dir] .preview-form-container .form-group .captcha-form {
  margin-top: 17px;
}
.preview-form-container .preview-header .inner-header-container {
  display: flex;
  align-items: center;
  justify-content: start;
}
[dir] .preview-form-container .preview-header .inner-header-container {
  background-color: #fcfcfc;
  border: 1px solid #e5e5e5;
  border-radius: 5px;
  padding: 12px 15px;
}
.preview-form-container .preview-header .inner-header-container .form-label {
  height: 100%;
  color: #4a4a4a;
  font-weight: 500;
  font-size: 0.95rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
[dir="ltr"]
  .preview-form-container
  .preview-header
  .inner-header-container
  .form-label {
  margin-left: 1rem;
}
[dir="rtl"]
  .preview-form-container
  .preview-header
  .inner-header-container
  .form-label {
  margin-right: 1rem;
}
.challenge-captcha-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, Lato, DroidKuffi, sans-serif;
}
[dir] .challenge-captcha-container {
  background-color: #f8f8f8;
}
[dir] .challenge-captcha-container .captcha-box {
  padding: 0 15px;
}
[dir] .challenge-captcha-container .captcha-box .cb-header {
  margin: 0 0 24px;
  text-align: center;
}
.challenge-captcha-container .captcha-box .cb-header .cb-logo {
  max-width: 150px;
}
[dir] .challenge-captcha-container .captcha-box .cb-header .cb-logo {
  margin: 0 auto 15px;
}
.challenge-captcha-container .captcha-box .cb-header .cb-title {
  font-size: 18px;
  font-weight: 600;
}
[dir] .challenge-captcha-container .captcha-box .cb-body {
  background-color: #fff;
  padding: 30px;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.05);
}
.challenge-captcha-container .captcha-box .cb-body .captcha-input {
  display: flex;
  justify-content: center;
}
.challenge-captcha-container .captcha-box .cb-body .cb-button {
  justify-content: center;
  width: 100%;
  color: #fff;
}
[dir] .challenge-captcha-container .captcha-box .cb-body .cb-button {
  border: 1px solid transparent;
  border-radius: 2px;
  padding: 12px 24px;
  cursor: pointer;
  background-color: #aa2e66;
}
@media (min-width: 600px) {
  .challenge-captcha-container .captcha-box {
    min-width: 530px;
    max-width: 900px;
  }
}
@media (max-width: 600px) {
  .challenge-captcha-container .captcha-box {
    min-width: 100%;
    max-width: 100%;
  }
}
